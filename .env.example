DATABASE_URL="postgresql://postgres:postgres@localhost:5432/vocab?schema=public"
TELEGRAM_BOT_TOKEN=
FEATURE_GOOGLE_LOGIN=false

JWT_COOKIE_NAME=auth_token
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=2592000

LLM_OPENAI_API_KEY=
LLM_OPENAI_MODEL=gpt-4.1-nano
LLM_MAX_EXAMPLES=8
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2000

# Redis Cache Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=vocab:

# Cache Configuration
CACHE_MEMORY_SIZE=1000
CACHE_DEFAULT_TTL=3600
CACHE_ENABLE_COMPRESSION=true
CACHE_ENABLE_ANALYTICS=true
