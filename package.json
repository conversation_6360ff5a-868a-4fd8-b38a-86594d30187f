{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "scan": "next dev --turbopack & npx react-scan@latest localhost:3000", "build": "yarn p:m && next build", "start": "next start", "lint": "npx tsc --noEmit --skipLibCheck --project .", "p:m": "prisma migrate dev", "p:m:r": "prisma migrate reset", "p:s": "prisma studio", "dup": "docker compose up -d"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@prisma/client": "6.10.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.10", "@types/bcryptjs": "^2.4.6", "@types/nprogress": "^0.2.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "eslint": "^9.30.0", "framer-motion": "^12.6.2", "i18next": "^23.7.13", "ioredis": "^5.6.1", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "lucide-react": "^0.483.0", "next": "^15.3.2", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "node-cache": "^5.1.2", "nprogress": "^0.2.0", "openai": "^5.8.2", "radix-ui": "^1.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^13.5.0", "react-scan": "^0.3.4", "react-window": "^1.8.11", "recharts": "^2.15.3", "redis": "^5.5.6", "server-only": "^0.0.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/compression": "^1.8.1", "@types/jsonwebtoken": "^9.0.9", "@types/lru-cache": "^7.10.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "@types/redis": "^4.0.11", "dotenv": "^16.4.7", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^10.1.1", "prisma": "6.10.1", "tailwindcss": "^4", "typescript": "^5", "typescript-eslint": "^8.28.0"}, "packageManager": "yarn@4.9.2"}