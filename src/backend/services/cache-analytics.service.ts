import { EnhancedCacheService } from './cache.service';
import { QueryCacheService } from './query-cache.service';
import { LLMCacheService } from './llm-cache.service';
import { CacheWarmingService } from './cache-warming.service';

// Cache performance metrics
export interface CachePerformanceMetrics {
	timestamp: number;
	memoryCache: {
		hitRate: number;
		missRate: number;
		size: number;
		maxSize: number;
		evictions: number;
	};
	redisCache: {
		hitRate: number;
		missRate: number;
		connections: number;
		latency: number;
	};
	queryCache: {
		totalKeys: number;
		hitRate: number;
		averageResponseTime: number;
		patternDistribution: Record<string, number>;
	};
	llmCache: {
		hitRate: number;
		costSavings: number;
		tokensSaved: number;
		totalRequests: number;
	};
	overall: {
		hitRate: number;
		averageResponseTime: number;
		totalDataSize: number;
		efficiency: number;
	};
}

// Cache optimization recommendation
export interface CacheOptimizationRecommendation {
	type: 'performance' | 'cost' | 'storage' | 'configuration';
	priority: 'low' | 'medium' | 'high' | 'critical';
	title: string;
	description: string;
	impact: string;
	implementation: string;
	estimatedImprovement: number; // Percentage
}

// Cache health status
export interface CacheHealthStatus {
	overall: 'healthy' | 'warning' | 'critical' | 'offline';
	components: {
		memory: 'healthy' | 'warning' | 'critical';
		redis: 'healthy' | 'warning' | 'critical' | 'offline';
		query: 'healthy' | 'warning' | 'critical';
		llm: 'healthy' | 'warning' | 'critical';
	};
	issues: string[];
	recommendations: CacheOptimizationRecommendation[];
}

// Cache usage pattern
export interface CacheUsagePattern {
	pattern: string;
	frequency: number;
	averageSize: number;
	hitRate: number;
	lastSeen: number;
	trend: 'increasing' | 'stable' | 'decreasing';
}

/**
 * Cache Analytics and Monitoring Service
 */
export class CacheAnalyticsService {
	private enhancedCache: EnhancedCacheService;
	private queryCache: QueryCacheService;
	private llmCache: LLMCacheService;
	private warmingService: CacheWarmingService;
	private metricsHistory: CachePerformanceMetrics[] = [];
	private usagePatterns: Map<string, CacheUsagePattern> = new Map();
	private alertThresholds = {
		hitRate: 0.7, // Alert if hit rate drops below 70%
		responseTime: 100, // Alert if response time exceeds 100ms
		memoryUsage: 0.9, // Alert if memory usage exceeds 90%
		errorRate: 0.05, // Alert if error rate exceeds 5%
	};

	constructor(
		enhancedCache: EnhancedCacheService,
		queryCache: QueryCacheService,
		llmCache: LLMCacheService,
		warmingService: CacheWarmingService
	) {
		this.enhancedCache = enhancedCache;
		this.queryCache = queryCache;
		this.llmCache = llmCache;
		this.warmingService = warmingService;
		this.startMetricsCollection();
	}

	/**
	 * Start continuous metrics collection
	 */
	private startMetricsCollection(): void {
		// Collect metrics every 5 minutes
		setInterval(() => {
			this.collectMetrics();
		}, 5 * 60 * 1000);

		// Analyze patterns every hour
		setInterval(() => {
			this.analyzeUsagePatterns();
		}, 60 * 60 * 1000);

		// Generate health report every 30 minutes
		setInterval(() => {
			this.generateHealthReport();
		}, 30 * 60 * 1000);
	}

	/**
	 * Collect current cache performance metrics
	 */
	async collectMetrics(): Promise<CachePerformanceMetrics> {
		const timestamp = Date.now();

		// Get stats from all cache layers
		const enhancedStats = this.enhancedCache.getStats();
		const queryStats = this.queryCache.getQueryCacheStats();
		const llmStats = this.llmCache.getCostSavingsStats();

		const metrics: CachePerformanceMetrics = {
			timestamp,
			memoryCache: {
				hitRate: enhancedStats.memory.hitRate,
				missRate: 1 - enhancedStats.memory.hitRate,
				size: enhancedStats.memory.size,
				maxSize: enhancedStats.memory.maxSize,
				evictions: 0, // Would be tracked in real implementation
			},
			redisCache: {
				hitRate: enhancedStats.redis.hitRate,
				missRate: 1 - enhancedStats.redis.hitRate,
				connections: enhancedStats.redis.connections,
				latency: 0, // Would be measured in real implementation
			},
			queryCache: {
				totalKeys: queryStats.totalKeys,
				hitRate: 0, // Would be calculated from query stats
				averageResponseTime: 0,
				patternDistribution: queryStats.patternDistribution,
			},
			llmCache: {
				hitRate: llmStats.cacheHitRate,
				costSavings: llmStats.estimatedCostSavings,
				tokensSaved: llmStats.totalTokensSaved,
				totalRequests: llmStats.totalRequests,
			},
			overall: {
				hitRate: enhancedStats.overall.overallHitRate,
				averageResponseTime: enhancedStats.overall.averageResponseTime,
				totalDataSize: 0, // Would be calculated
				efficiency: this.calculateOverallEfficiency(enhancedStats, llmStats),
			},
		};

		// Store metrics in history
		this.metricsHistory.push(metrics);

		// Keep only last 24 hours of metrics (288 data points at 5-minute intervals)
		if (this.metricsHistory.length > 288) {
			this.metricsHistory = this.metricsHistory.slice(-288);
		}

		return metrics;
	}

	/**
	 * Calculate overall cache efficiency
	 */
	private calculateOverallEfficiency(enhancedStats: any, llmStats: any): number {
		const hitRateWeight = 0.4;
		const responseTimeWeight = 0.3;
		const costSavingsWeight = 0.3;

		const hitRateScore = enhancedStats.overall.overallHitRate;
		const responseTimeScore = Math.max(0, 1 - enhancedStats.overall.averageResponseTime / 1000);
		const costSavingsScore = Math.min(1, llmStats.estimatedCostSavings / 100);

		return (
			hitRateScore * hitRateWeight +
			responseTimeScore * responseTimeWeight +
			costSavingsScore * costSavingsWeight
		);
	}

	/**
	 * Analyze cache usage patterns
	 */
	private analyzeUsagePatterns(): void {
		const queryStats = this.queryCache.getQueryCacheStats();

		for (const [pattern, count] of Object.entries(queryStats.patternDistribution)) {
			const existing = this.usagePatterns.get(pattern);
			const now = Date.now();

			if (existing) {
				// Update existing pattern
				const trend =
					count > existing.frequency
						? 'increasing'
						: count < existing.frequency
						? 'decreasing'
						: 'stable';

				this.usagePatterns.set(pattern, {
					...existing,
					frequency: count,
					lastSeen: now,
					trend,
				});
			} else {
				// New pattern
				this.usagePatterns.set(pattern, {
					pattern,
					frequency: count,
					averageSize: 1000, // Estimate
					hitRate: 0.8, // Estimate
					lastSeen: now,
					trend: 'stable',
				});
			}
		}
	}

	/**
	 * Generate cache health report
	 */
	generateHealthReport(): CacheHealthStatus {
		const latestMetrics = this.metricsHistory[this.metricsHistory.length - 1];
		if (!latestMetrics) {
			return {
				overall: 'offline',
				components: {
					memory: 'critical',
					redis: 'offline',
					query: 'critical',
					llm: 'critical',
				},
				issues: ['No metrics available'],
				recommendations: [],
			};
		}

		const issues: string[] = [];
		const recommendations: CacheOptimizationRecommendation[] = [];

		// Analyze memory cache health
		const memoryHealth = this.analyzeMemoryCacheHealth(latestMetrics, issues, recommendations);

		// Analyze Redis cache health
		const redisHealth = this.analyzeRedisCacheHealth(latestMetrics, issues, recommendations);

		// Analyze query cache health
		const queryHealth = this.analyzeQueryCacheHealth(latestMetrics, issues, recommendations);

		// Analyze LLM cache health
		const llmHealth = this.analyzeLLMCacheHealth(latestMetrics, issues, recommendations);

		// Determine overall health
		const componentHealths = [memoryHealth, redisHealth, queryHealth, llmHealth];
		const overall = componentHealths.includes('critical')
			? 'critical'
			: componentHealths.includes('warning')
			? 'warning'
			: 'healthy';

		return {
			overall,
			components: {
				memory: memoryHealth,
				redis: redisHealth,
				query: queryHealth,
				llm: llmHealth,
			},
			issues,
			recommendations,
		};
	}

	/**
	 * Analyze memory cache health
	 */
	private analyzeMemoryCacheHealth(
		metrics: CachePerformanceMetrics,
		issues: string[],
		recommendations: CacheOptimizationRecommendation[]
	): 'healthy' | 'warning' | 'critical' {
		const { memoryCache } = metrics;

		if (memoryCache.hitRate < 0.5) {
			issues.push('Memory cache hit rate is critically low');
			recommendations.push({
				type: 'performance',
				priority: 'critical',
				title: 'Improve Memory Cache Hit Rate',
				description: 'Memory cache hit rate is below 50%',
				impact: 'Significant performance degradation',
				implementation: 'Increase cache size or improve cache warming',
				estimatedImprovement: 30,
			});
			return 'critical';
		}

		if (memoryCache.size / memoryCache.maxSize > 0.9) {
			issues.push('Memory cache is near capacity');
			recommendations.push({
				type: 'storage',
				priority: 'high',
				title: 'Increase Memory Cache Size',
				description: 'Memory cache is over 90% full',
				impact: 'Increased evictions and reduced performance',
				implementation: 'Increase maxSize configuration',
				estimatedImprovement: 15,
			});
			return 'warning';
		}

		if (memoryCache.hitRate < this.alertThresholds.hitRate) {
			issues.push('Memory cache hit rate is below threshold');
			return 'warning';
		}

		return 'healthy';
	}

	/**
	 * Analyze Redis cache health
	 */
	private analyzeRedisCacheHealth(
		metrics: CachePerformanceMetrics,
		issues: string[],
		recommendations: CacheOptimizationRecommendation[]
	): 'healthy' | 'warning' | 'critical' | 'offline' {
		const { redisCache } = metrics;

		if (redisCache.connections === 0) {
			issues.push('Redis cache is offline');
			return 'offline';
		}

		if (redisCache.hitRate < 0.4) {
			issues.push('Redis cache hit rate is critically low');
			recommendations.push({
				type: 'configuration',
				priority: 'high',
				title: 'Optimize Redis Configuration',
				description: 'Redis hit rate is below 40%',
				impact: 'Increased database load',
				implementation: 'Review TTL settings and cache warming strategy',
				estimatedImprovement: 25,
			});
			return 'critical';
		}

		if (redisCache.latency > 50) {
			issues.push('Redis latency is high');
			return 'warning';
		}

		return 'healthy';
	}

	/**
	 * Analyze query cache health
	 */
	private analyzeQueryCacheHealth(
		metrics: CachePerformanceMetrics,
		issues: string[],
		recommendations: CacheOptimizationRecommendation[]
	): 'healthy' | 'warning' | 'critical' {
		const { queryCache } = metrics;

		if (queryCache.averageResponseTime > this.alertThresholds.responseTime) {
			issues.push('Query cache response time is high');
			recommendations.push({
				type: 'performance',
				priority: 'medium',
				title: 'Optimize Query Cache Performance',
				description: 'Average response time exceeds threshold',
				impact: 'Slower application response times',
				implementation: 'Review cache key generation and storage efficiency',
				estimatedImprovement: 20,
			});
			return 'warning';
		}

		if (queryCache.totalKeys > 10000) {
			issues.push('Query cache has too many keys');
			recommendations.push({
				type: 'storage',
				priority: 'medium',
				title: 'Implement Cache Key Cleanup',
				description: 'Query cache has excessive number of keys',
				impact: 'Memory usage and lookup performance',
				implementation: 'Implement automatic cleanup of expired keys',
				estimatedImprovement: 10,
			});
			return 'warning';
		}

		return 'healthy';
	}

	/**
	 * Analyze LLM cache health
	 */
	private analyzeLLMCacheHealth(
		metrics: CachePerformanceMetrics,
		issues: string[],
		recommendations: CacheOptimizationRecommendation[]
	): 'healthy' | 'warning' | 'critical' {
		const { llmCache } = metrics;

		if (llmCache.hitRate < 0.3) {
			issues.push('LLM cache hit rate is low');
			recommendations.push({
				type: 'cost',
				priority: 'high',
				title: 'Improve LLM Cache Strategy',
				description: 'LLM cache hit rate is below 30%',
				impact: 'High API costs and slow response times',
				implementation: 'Implement better prompt normalization and cache warming',
				estimatedImprovement: 40,
			});
			return 'critical';
		}

		if (llmCache.costSavings < 10) {
			issues.push('LLM cache cost savings are minimal');
			recommendations.push({
				type: 'cost',
				priority: 'medium',
				title: 'Optimize LLM Cache for Cost Savings',
				description: 'Current cost savings are below expectations',
				impact: 'Higher than necessary API costs',
				implementation: 'Increase cache TTL for stable responses',
				estimatedImprovement: 25,
			});
			return 'warning';
		}

		return 'healthy';
	}

	/**
	 * Get cache performance trends
	 */
	getPerformanceTrends(hours: number = 24): {
		hitRateTrend: number[];
		responseTimeTrend: number[];
		costSavingsTrend: number[];
		timestamps: number[];
	} {
		const cutoff = Date.now() - hours * 60 * 60 * 1000;
		const recentMetrics = this.metricsHistory.filter((m) => m.timestamp > cutoff);

		return {
			hitRateTrend: recentMetrics.map((m) => m.overall.hitRate),
			responseTimeTrend: recentMetrics.map((m) => m.overall.averageResponseTime),
			costSavingsTrend: recentMetrics.map((m) => m.llmCache.costSavings),
			timestamps: recentMetrics.map((m) => m.timestamp),
		};
	}

	/**
	 * Get current cache statistics
	 */
	getCurrentStats(): CachePerformanceMetrics | null {
		return this.metricsHistory[this.metricsHistory.length - 1] || null;
	}

	/**
	 * Get usage patterns
	 */
	getUsagePatterns(): CacheUsagePattern[] {
		return Array.from(this.usagePatterns.values()).sort((a, b) => b.frequency - a.frequency);
	}

	/**
	 * Generate optimization recommendations
	 */
	generateOptimizationRecommendations(): CacheOptimizationRecommendation[] {
		const healthReport = this.generateHealthReport();
		return healthReport.recommendations;
	}

	/**
	 * Export metrics for external analysis
	 */
	exportMetrics(format: 'json' | 'csv' = 'json'): string {
		if (format === 'json') {
			return JSON.stringify(this.metricsHistory, null, 2);
		}

		// CSV format
		const headers = ['timestamp', 'hitRate', 'responseTime', 'costSavings'];
		const rows = this.metricsHistory.map((m) => [
			m.timestamp,
			m.overall.hitRate,
			m.overall.averageResponseTime,
			m.llmCache.costSavings,
		]);

		return [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');
	}
}
