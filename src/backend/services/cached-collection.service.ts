import { CollectionServiceImpl } from './collection.service';
import { QueryCacheService } from './query-cache.service';
import { CollectionWithDetail } from '@/models';
import { Language } from '@prisma/client';
import { CollectionRepository, LLMService, WordService } from '@/backend/services';

/**
 * Simple Cached Collection Service with basic query caching
 */
export class CachedCollectionService extends CollectionServiceImpl {
	private queryCacheService: QueryCacheService;

	constructor(
		getCollectionRepository: () => CollectionRepository,
		getLLMService: () => LLMService,
		getWordService: () => WordService,
		queryCacheService: QueryCacheService
	) {
		super(getCollectionRepository, getLLMService, getWordService);
		this.queryCacheService = queryCacheService;
	}

	getQueryCacheService(): QueryCacheService {
		return this.queryCacheService;
	}

	/**
	 * Get user collections with caching
	 */
	async getUserCollections(userId: string): Promise<CollectionWithDetail[]> {
		try {
			const cached = await this.queryCacheService.getCachedQuery(
				'CollectionService',
				'getUserCollections',
				{ userId }
			);
			if (cached !== null) return cached;

			const result = await super.getUserCollections(userId);
			await this.queryCacheService.setCachedQuery(
				'CollectionService',
				'getUserCollections',
				{ userId },
				result,
				undefined,
				60 * 30 // 30 minutes
			);
			return result;
		} catch (_error) {
			return super.getUserCollections(userId);
		}
	}

	/**
	 * Get collection by ID with caching
	 */
	async getCollectionById(userId: string, collectionId: string): Promise<CollectionWithDetail | null> {
		try {
			const cached = await this.queryCacheService.getCachedQuery(
				'CollectionService',
				'getCollectionById',
				{ userId, collectionId }
			);
			if (cached !== null) return cached;

			const result = await super.getCollectionById(userId, collectionId);
			await this.queryCacheService.setCachedQuery(
				'CollectionService',
				'getCollectionById',
				{ userId, collectionId },
				result,
				undefined,
				60 * 60 // 1 hour
			);
			return result;
		} catch (_error) {
			return super.getCollectionById(userId, collectionId);
		}
	}

	/**
	 * Create collection with cache invalidation
	 */
	async createCollection(
		userId: string,
		name: string,
		target_language: Language,
		source_language: Language,
		wordIds?: string[]
	): Promise<CollectionWithDetail> {
		const result = await super.createCollection(userId, name, target_language, source_language, wordIds);
		
		// Invalidate user collections cache
		try {
			await this.queryCacheService.invalidateEntity('user', userId);
			await this.queryCacheService.invalidateByPattern('collection:userCollections:*');
		} catch (_error) {
			// Ignore cache invalidation errors
		}
		
		return result;
	}

	/**
	 * Update collection with cache invalidation
	 */
	async updateCollection(
		userId: string,
		collectionId: string,
		name?: string,
		wordIds?: string[]
	): Promise<CollectionWithDetail | null> {
		const result = await super.updateCollection(userId, collectionId, name, wordIds);
		
		// Invalidate specific collection and user collections cache
		try {
			await this.queryCacheService.invalidateEntity('collection', collectionId);
			await this.queryCacheService.invalidateEntity('user', userId);
		} catch (_error) {
			// Ignore cache invalidation errors
		}
		
		return result;
	}

	/**
	 * Delete collection with cache invalidation
	 */
	async deleteCollection(userId: string, collectionId: string): Promise<boolean> {
		const result = await super.deleteCollection(userId, collectionId);
		
		if (result) {
			// Invalidate all related caches
			try {
				await this.queryCacheService.invalidateEntity('collection', collectionId);
				await this.queryCacheService.invalidateEntity('user', userId);
				await this.queryCacheService.invalidateByPattern('word:byCollection:*');
			} catch (_error) {
				// Ignore cache invalidation errors
			}
		}
		
		return result;
	}

	/**
	 * Add words to collection with cache invalidation
	 */
	async addWordsToCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null> {
		const result = await super.addWordsToCollection(userId, collectionId, wordIds);
		
		// Invalidate collection-related caches
		try {
			await this.queryCacheService.invalidateEntity('collection', collectionId);
			await this.queryCacheService.invalidateByPattern('word:byCollection:*');
		} catch (_error) {
			// Ignore cache invalidation errors
		}
		
		return result;
	}

	/**
	 * Get cache performance metrics for collections
	 */
	getCollectionCacheMetrics(): any {
		return this.queryCacheService.getQueryCacheStats();
	}
}
