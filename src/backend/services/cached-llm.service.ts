import { LLMService } from './llm.service';
import { LLMCacheService } from './llm-cache.service';
import { WordService } from './word.service';
import {
	GenerateQuestionsParams,
	GenerateParagraphWithQuestionsParams,
	EvaluateAnswersParams,
	EvaluateTranslationParams,
	GrammarPracticeParams,
	ParagraphWithQuestionsResult,
	AnswerEvaluationResult,
	GrammarPracticeResultItem,
} from './llm.service';
import { Language, Word } from '@prisma/client';

/**
 * Simple Cached LLM Service with basic response caching
 */
export class CachedLLMService extends LLMService {
	private llmCacheService: LLMCacheService;

	constructor(getWordService: () => WordService, llmCacheService: LLMCacheService) {
		super(getWordService);
		this.llmCacheService = llmCacheService;
	}

	getLLMCacheService(): LLMCacheService {
		return this.llmCacheService;
	}

	// Note: generateRandomWords method removed as it doesn't exist in base LLMService

	/**
	 * Generate word details with caching
	 */
	async generateWordDetails(
		terms: string[],
		sourceLanguage: Language,
		targetLanguage: Language
	): Promise<Word[]> {
		try {
			const prompt = `${terms.join(',')}:${sourceLanguage}:${targetLanguage}`;
			const cached = await this.llmCacheService.getCachedResponse(
				'generateWordDetails',
				prompt,
				'gpt-4',
				0.3,
				1000
			);
			if (cached !== null && cached !== undefined) return cached as Word[];

			const result = await super.generateWordDetails(terms, sourceLanguage, targetLanguage);
			await this.llmCacheService.setCachedResponse(
				'generateWordDetails',
				prompt,
				'gpt-4',
				0.3,
				1000,
				result,
				800 // Estimated tokens
			);
			return result;
		} catch (_error) {
			return super.generateWordDetails(terms, sourceLanguage, targetLanguage);
		}
	}

	/**
	 * Generate questions with caching
	 */
	async generateQuestions(params: GenerateQuestionsParams): Promise<string[]> {
		try {
			const cached = await this.llmCacheService.getCachedResponse(
				'generateQuestions',
				JSON.stringify(params),
				'gpt-4',
				0.7,
				1000
			);
			if (cached !== null && cached !== undefined) return cached as string[];

			const result = await super.generateQuestions(params);
			await this.llmCacheService.setCachedResponse(
				'generateQuestions',
				JSON.stringify(params),
				'gpt-4',
				0.7,
				1000,
				result,
				600 // Estimated tokens
			);
			return result;
		} catch (_error) {
			return super.generateQuestions(params);
		}
	}

	/**
	 * Generate paragraph with questions with caching
	 */
	async generateParagraphWithQuestions(
		params: GenerateParagraphWithQuestionsParams
	): Promise<ParagraphWithQuestionsResult> {
		try {
			const cached = await this.llmCacheService.getCachedResponse(
				'generateParagraphWithQuestions',
				JSON.stringify(params),
				'gpt-4',
				0.7,
				2000
			);
			if (cached !== null && cached !== undefined)
				return cached as ParagraphWithQuestionsResult;

			const result = await super.generateParagraphWithQuestions(params);
			await this.llmCacheService.setCachedResponse(
				'generateParagraphWithQuestions',
				JSON.stringify(params),
				'gpt-4',
				0.7,
				2000,
				result,
				1200 // Estimated tokens
			);
			return result;
		} catch (_error) {
			return super.generateParagraphWithQuestions(params);
		}
	}

	/**
	 * Evaluate answers with caching
	 */
	async evaluateAnswers(params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]> {
		try {
			const cached = await this.llmCacheService.getCachedResponse(
				'evaluateAnswers',
				JSON.stringify(params),
				'gpt-4',
				0.3,
				1000
			);
			if (cached !== null && cached !== undefined) return cached as AnswerEvaluationResult[];

			const result = await super.evaluateAnswers(params);
			await this.llmCacheService.setCachedResponse(
				'evaluateAnswers',
				JSON.stringify(params),
				'gpt-4',
				0.3,
				1000,
				result,
				700 // Estimated tokens
			);
			return result;
		} catch (_error) {
			return super.evaluateAnswers(params);
		}
	}

	/**
	 * Evaluate translation with caching
	 */
	async evaluateTranslation(params: EvaluateTranslationParams): Promise<any> {
		try {
			const cached = await this.llmCacheService.getCachedResponse(
				'evaluateTranslation',
				JSON.stringify(params),
				'gpt-4',
				0.3,
				1000
			);
			if (cached !== null && cached !== undefined) return cached as any;

			const result = await super.evaluateTranslation(params);
			await this.llmCacheService.setCachedResponse(
				'evaluateTranslation',
				JSON.stringify(params),
				'gpt-4',
				0.3,
				1000,
				result,
				600 // Estimated tokens
			);
			return result;
		} catch (_error) {
			return super.evaluateTranslation(params);
		}
	}

	/**
	 * Generate grammar practice with caching
	 */
	async generateGrammarPractice(
		params: GrammarPracticeParams
	): Promise<GrammarPracticeResultItem[]> {
		try {
			const cached = await this.llmCacheService.getCachedResponse(
				'generateGrammarPractice',
				JSON.stringify(params),
				'gpt-4',
				0.7,
				1500
			);
			if (cached !== null && cached !== undefined)
				return cached as GrammarPracticeResultItem[];

			const result = await super.generateGrammarPractice(params);
			await this.llmCacheService.setCachedResponse(
				'generateGrammarPractice',
				JSON.stringify(params),
				'gpt-4',
				0.7,
				1500,
				result,
				900 // Estimated tokens
			);
			return result;
		} catch (_error) {
			return super.generateGrammarPractice(params);
		}
	}

	/**
	 * Get LLM cache performance metrics
	 */
	getLLMCacheMetrics(): any {
		return this.llmCacheService.getCostSavingsStats();
	}

	/**
	 * Get LLM cache health status
	 */
	getLLMCacheHealth(): any {
		return this.llmCacheService.getLLMCacheHealth();
	}

	/**
	 * Clear LLM cache
	 */
	async clearLLMCache(): Promise<void> {
		await this.llmCacheService.clearLLMCache();
	}
}
