import { WordServiceImpl } from './word.service';
import { QueryCacheService } from './query-cache.service';
import { WordDetail } from '@/models/word';
import { Language } from '@prisma/client';
import { WordRepository, CollectionService, LastSeenWordService } from '@/backend/services';

/**
 * Simple Cached Word Service with basic query caching
 */
export class CachedWordService extends WordServiceImpl {
	private queryCacheService: QueryCacheService;

	constructor(
		getWordRepository: () => WordRepository,
		getCollectionService: () => CollectionService,
		getLastSeenWordService: () => LastSeenWordService,
		queryCacheService: QueryCacheService
	) {
		super(getWordRepository, getCollectionService, getLastSeenWordService);
		this.queryCacheService = queryCacheService;
	}

	getQueryCacheService(): QueryCacheService {
		return this.queryCacheService;
	}

	/**
	 * Search words with caching
	 */
	async searchWords(term: string, language?: Language, limit = 10): Promise<WordDetail[]> {
		try {
			const cached = await this.queryCacheService.getCachedQuery(
				'WordService',
				'searchWords',
				{ term, language, limit }
			);

			if (cached !== null) {
				return cached;
			}

			const result = await super.searchWords(term, language, limit);

			await this.queryCacheService.setCachedQuery(
				'WordService',
				'searchWords',
				{ term, language, limit },
				result,
				undefined,
				60 * 30 // 30 minutes
			);

			return result;
		} catch (_error) {
			// Fallback to original method if caching fails
			return super.searchWords(term, language, limit);
		}
	}

	/**
	 * Get word by ID with caching
	 */
	async getWordById(wordId: string): Promise<WordDetail | null> {
		try {
			const cached = await this.queryCacheService.getCachedQuery('WordService', 'getWordById', { wordId });
			if (cached !== null) return cached;
			
			const result = await super.getWordById(wordId);
			await this.queryCacheService.setCachedQuery('WordService', 'getWordById', { wordId }, result, undefined, 60 * 60);
			return result;
		} catch (_error) {
			return super.getWordById(wordId);
		}
	}

	/**
	 * Create word with cache invalidation
	 */
	async createWordWithRandomWordDetail(word: any): Promise<WordDetail> {
		const result = await super.createWordWithRandomWordDetail(word);
		
		// Invalidate related caches
		try {
			await this.queryCacheService.invalidateByPattern('word:*');
			await this.queryCacheService.invalidateByPattern('collection:*');
		} catch (_error) {
			// Ignore cache invalidation errors
		}
		
		return result;
	}

	/**
	 * Bulk delete words with cache invalidation
	 */
	async bulkDeleteWordsFromCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<number> {
		const result = await super.bulkDeleteWordsFromCollection(userId, collectionId, wordIds);
		
		// Invalidate related caches
		try {
			await this.queryCacheService.invalidateEntity('collection', collectionId);
			await this.queryCacheService.invalidateByPattern('word:*');
		} catch (_error) {
			// Ignore cache invalidation errors
		}
		
		return result;
	}

	/**
	 * Get cache performance metrics for words
	 */
	getWordCacheMetrics(): any {
		return this.queryCacheService.getQueryCacheStats();
	}
}
