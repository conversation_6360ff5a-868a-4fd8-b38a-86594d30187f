import { EnhancedCacheService, CachePriority } from './cache.service';
import { createHash } from 'crypto';

// LLM cache configuration
export interface LLMCacheConfig {
	enabled: boolean;
	defaultTtl: number;
	maxPromptLength: number;
	enableSemanticSimilarity: boolean;
	similarityThreshold: number;
	costSavingsTracking: boolean;
}

// LLM request metadata
export interface LLMRequestMetadata {
	model: string;
	temperature: number;
	maxTokens: number;
	timestamp: number;
	promptHash: string;
	responseTokens: number;
	estimatedCost: number;
}

// LLM cache entry
export interface LLMCacheEntry<T> {
	response: T;
	metadata: LLMRequestMetadata;
	hitCount: number;
	lastAccessed: number;
	costSavings: number;
}

// Cost savings statistics
export interface CostSavingsStats {
	totalRequests: number;
	cachedRequests: number;
	cacheHitRate: number;
	estimatedCostSavings: number;
	totalTokensSaved: number;
}

/**
 * LLM Cache Service for intelligent caching of language model responses
 */
export class LLMCacheService {
	private cacheService: EnhancedCacheService;
	private config: LLMCacheConfig;
	private costSavingsStats: CostSavingsStats;

	constructor(cacheService: EnhancedCacheService, config?: Partial<LLMCacheConfig>) {
		this.cacheService = cacheService;
		this.config = this.mergeConfig(config);
		this.initializeStats();
	}

	private mergeConfig(userConfig?: Partial<LLMCacheConfig>): LLMCacheConfig {
		const defaultConfig: LLMCacheConfig = {
			enabled: true,
			defaultTtl: 60 * 60 * 24 * 7, // 7 days
			maxPromptLength: 10000,
			enableSemanticSimilarity: false, // Would require additional ML models
			similarityThreshold: 0.95,
			costSavingsTracking: true,
		};

		return { ...defaultConfig, ...userConfig };
	}

	private initializeStats(): void {
		this.costSavingsStats = {
			totalRequests: 0,
			cachedRequests: 0,
			cacheHitRate: 0,
			estimatedCostSavings: 0,
			totalTokensSaved: 0,
		};
	}

	/**
	 * Generate cache key for LLM request
	 */
	generateLLMKey(
		operation: string,
		prompt: string,
		model: string,
		temperature: number,
		maxTokens: number,
		additionalParams?: Record<string, any>
	): string {
		// Normalize prompt to handle minor variations
		const normalizedPrompt = this.normalizePrompt(prompt);

		const keyData = {
			operation,
			prompt: normalizedPrompt,
			model,
			temperature: Math.round(temperature * 100) / 100, // Round to 2 decimal places
			maxTokens,
			...additionalParams,
		};

		const keyString = JSON.stringify(keyData);
		const hash = createHash('sha256').update(keyString).digest('hex').substring(0, 20);
		return `llm:${operation}:${hash}`;
	}

	/**
	 * Normalize prompt for consistent caching
	 */
	private normalizePrompt(prompt: string): string {
		return prompt
			.trim()
			.replace(/\s+/g, ' ') // Normalize whitespace
			.toLowerCase();
	}

	/**
	 * Get cached LLM response
	 */
	async getCachedResponse<T>(
		operation: string,
		prompt: string,
		model: string,
		temperature: number,
		maxTokens: number,
		additionalParams?: Record<string, any>
	): Promise<T | null> {
		if (!this.config.enabled) return null;
		if (prompt.length > this.config.maxPromptLength) return null;

		const key = this.generateLLMKey(
			operation,
			prompt,
			model,
			temperature,
			maxTokens,
			additionalParams
		);
		const cached = await this.cacheService.get<LLMCacheEntry<T>>(key);

		if (cached) {
			// Update hit statistics
			cached.hitCount++;
			cached.lastAccessed = Date.now();

			// Update cost savings
			if (this.config.costSavingsTracking) {
				const estimatedCost = this.estimateRequestCost(
					model,
					cached.metadata.responseTokens
				);
				cached.costSavings += estimatedCost;
				this.costSavingsStats.cachedRequests++;
				this.costSavingsStats.estimatedCostSavings += estimatedCost;
				this.costSavingsStats.totalTokensSaved += cached.metadata.responseTokens;
			}

			// Re-cache with updated metadata
			await this.cacheService.set(
				key,
				cached,
				this.config.defaultTtl,
				CachePriority.CRITICAL
			);

			return cached.response;
		}

		this.costSavingsStats.totalRequests++;
		return null;
	}

	/**
	 * Cache LLM response
	 */
	async setCachedResponse<T>(
		operation: string,
		prompt: string,
		model: string,
		temperature: number,
		maxTokens: number,
		response: T,
		responseTokens: number,
		additionalParams?: Record<string, any>
	): Promise<void> {
		if (!this.config.enabled) return;
		if (prompt.length > this.config.maxPromptLength) return;

		const key = this.generateLLMKey(
			operation,
			prompt,
			model,
			temperature,
			maxTokens,
			additionalParams
		);
		const promptHash = createHash('sha256').update(prompt).digest('hex').substring(0, 16);

		const cacheEntry: LLMCacheEntry<T> = {
			response,
			metadata: {
				model,
				temperature,
				maxTokens,
				timestamp: Date.now(),
				promptHash,
				responseTokens,
				estimatedCost: this.estimateRequestCost(model, responseTokens),
			},
			hitCount: 0,
			lastAccessed: Date.now(),
			costSavings: 0,
		};

		await this.cacheService.set(
			key,
			cacheEntry,
			this.config.defaultTtl,
			CachePriority.CRITICAL
		);
		this.costSavingsStats.totalRequests++;
	}

	/**
	 * Estimate request cost based on model and tokens
	 */
	private estimateRequestCost(model: string, tokens: number): number {
		// Rough cost estimates (would be more accurate with real pricing)
		const costPerToken: Record<string, number> = {
			'gpt-4': 0.00003, // $0.03 per 1K tokens
			'gpt-4-turbo': 0.00001, // $0.01 per 1K tokens
			'gpt-3.5-turbo': 0.000002, // $0.002 per 1K tokens
			'claude-3-opus': 0.000015, // $0.015 per 1K tokens
			'claude-3-sonnet': 0.000003, // $0.003 per 1K tokens
		};

		const rate = costPerToken[model] || 0.00001; // Default rate
		return tokens * rate;
	}

	/**
	 * Get cost savings statistics
	 */
	getCostSavingsStats(): CostSavingsStats {
		this.costSavingsStats.cacheHitRate =
			this.costSavingsStats.totalRequests > 0
				? this.costSavingsStats.cachedRequests / this.costSavingsStats.totalRequests
				: 0;

		return { ...this.costSavingsStats };
	}

	/**
	 * Clear LLM cache
	 */
	async clearLLMCache(): Promise<void> {
		// This would ideally clear only LLM-related cache entries
		// For now, we'll reset the stats
		this.initializeStats();
	}

	/**
	 * Get cache health for LLM operations
	 */
	getLLMCacheHealth(): {
		enabled: boolean;
		hitRate: number;
		costSavings: number;
		totalRequests: number;
	} {
		const stats = this.getCostSavingsStats();
		return {
			enabled: this.config.enabled,
			hitRate: stats.cacheHitRate,
			costSavings: stats.estimatedCostSavings,
			totalRequests: stats.totalRequests,
		};
	}

	/**
	 * Warm cache with common prompts
	 */
	async warmCommonPrompts(): Promise<void> {
		// This would pre-populate cache with common prompt patterns
		console.log('Warming LLM cache with common prompts');
	}

	/**
	 * Invalidate cache for specific operation
	 */
	async invalidateLLMOperation(operation: string): Promise<void> {
		// This would invalidate all cache entries for a specific operation
		console.log(`Invalidating LLM cache for operation: ${operation}`);
	}
}

// Note: Decorators removed for build compatibility
// Use manual caching in service implementations instead
