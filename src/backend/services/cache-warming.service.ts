import { EnhancedCacheService, CachePriority } from './cache.service';
import { QueryCacheService } from './query-cache.service';
import { LLMCacheService } from './llm-cache.service';
import { Language } from '@prisma/client';

// User behavior pattern interface
export interface UserBehaviorPattern {
	userId: string;
	mostAccessedCollections: string[];
	preferredLanguages: Language[];
	activeTimeRanges: { start: number; end: number }[];
	searchPatterns: string[];
	lastActivity: number;
	sessionDuration: number;
	deviceType: 'mobile' | 'desktop' | 'tablet';
}

// Popular content metrics
export interface PopularContentMetrics {
	popularWords: { term: string; language: Language; accessCount: number }[];
	popularCollections: { id: string; accessCount: number; userCount: number }[];
	trendingSearches: { query: string; frequency: number; language?: Language }[];
	commonQuestionPatterns: { pattern: string; frequency: number }[];
}

// Cache warming job configuration
export interface CacheWarmingJobConfig {
	id: string;
	name: string;
	enabled: boolean;
	schedule: string; // Cron expression
	priority: CachePriority;
	estimatedDuration: number; // in milliseconds
	targetCacheTypes: ('memory' | 'redis' | 'query' | 'llm')[];
	conditions?: {
		minUserActivity?: number;
		timeRanges?: { start: string; end: string }[];
		userSegments?: string[];
	};
}

// Cache warming result
export interface CacheWarmingResult {
	jobId: string;
	startTime: number;
	endTime: number;
	itemsWarmed: number;
	cacheHitImprovement: number;
	errors: string[];
	performance: {
		averageWarmTime: number;
		totalDataSize: number;
		cacheEfficiency: number;
	};
}

/**
 * Comprehensive Cache Warming and Prefetching Service
 */
export class CacheWarmingService {
	private enhancedCache: EnhancedCacheService;
	private queryCache: QueryCacheService;
	private llmCache: LLMCacheService;
	private userBehaviorPatterns: Map<string, UserBehaviorPattern> = new Map();
	private popularContentMetrics: PopularContentMetrics;
	private warmingJobs: Map<string, CacheWarmingJobConfig> = new Map();
	private isWarmingActive: boolean = false;

	constructor(
		enhancedCache: EnhancedCacheService,
		queryCache: QueryCacheService,
		llmCache: LLMCacheService
	) {
		this.enhancedCache = enhancedCache;
		this.queryCache = queryCache;
		this.llmCache = llmCache;
		this.initializePopularContentMetrics();
		this.registerDefaultWarmingJobs();
	}

	private initializePopularContentMetrics(): void {
		this.popularContentMetrics = {
			popularWords: [],
			popularCollections: [],
			trendingSearches: [],
			commonQuestionPatterns: [],
		};
	}

	private registerDefaultWarmingJobs(): void {
		// Popular content warming
		this.registerWarmingJob({
			id: 'warm-popular-content',
			name: 'Warm Popular Content',
			enabled: true,
			schedule: '0 */6 * * *', // Every 6 hours
			priority: CachePriority.HIGH,
			estimatedDuration: 300000, // 5 minutes
			targetCacheTypes: ['memory', 'redis', 'query'],
		});

		// User-specific warming
		this.registerWarmingJob({
			id: 'warm-user-patterns',
			name: 'Warm User Behavior Patterns',
			enabled: true,
			schedule: '0 */2 * * *', // Every 2 hours
			priority: CachePriority.MEDIUM,
			estimatedDuration: 600000, // 10 minutes
			targetCacheTypes: ['memory', 'query'],
		});

		// LLM response warming
		this.registerWarmingJob({
			id: 'warm-llm-responses',
			name: 'Warm Common LLM Responses',
			enabled: true,
			schedule: '0 */8 * * *', // Every 8 hours
			priority: CachePriority.CRITICAL,
			estimatedDuration: 900000, // 15 minutes
			targetCacheTypes: ['llm'],
		});

		// Predictive warming based on time patterns
		this.registerWarmingJob({
			id: 'predictive-warming',
			name: 'Predictive Content Warming',
			enabled: true,
			schedule: '0 */1 * * *', // Every hour
			priority: CachePriority.MEDIUM,
			estimatedDuration: 180000, // 3 minutes
			targetCacheTypes: ['memory', 'query'],
			conditions: {
				minUserActivity: 10,
				timeRanges: [
					{ start: '06:00', end: '10:00' }, // Morning peak
					{ start: '18:00', end: '22:00' }, // Evening peak
				],
			},
		});
	}

	/**
	 * Register a cache warming job
	 */
	registerWarmingJob(config: CacheWarmingJobConfig): void {
		this.warmingJobs.set(config.id, config);
	}

	/**
	 * Execute a specific warming job
	 */
	async executeWarmingJob(jobId: string): Promise<CacheWarmingResult> {
		const job = this.warmingJobs.get(jobId);
		if (!job || !job.enabled) {
			throw new Error(`Warming job ${jobId} not found or disabled`);
		}

		const startTime = Date.now();
		const result: CacheWarmingResult = {
			jobId,
			startTime,
			endTime: 0,
			itemsWarmed: 0,
			cacheHitImprovement: 0,
			errors: [],
			performance: {
				averageWarmTime: 0,
				totalDataSize: 0,
				cacheEfficiency: 0,
			},
		};

		try {
			this.isWarmingActive = true;

			switch (jobId) {
				case 'warm-popular-content':
					result.itemsWarmed = await this.warmPopularContent();
					break;
				case 'warm-user-patterns':
					result.itemsWarmed = await this.warmUserPatterns();
					break;
				case 'warm-llm-responses':
					result.itemsWarmed = await this.warmLLMResponses();
					break;
				case 'predictive-warming':
					result.itemsWarmed = await this.executePredictiveWarming();
					break;
				default:
					throw new Error(`Unknown warming job: ${jobId}`);
			}

			result.endTime = Date.now();
			result.performance.averageWarmTime = (result.endTime - startTime) / result.itemsWarmed;

			console.log(
				`Cache warming job ${job.name} completed: ${result.itemsWarmed} items warmed`
			);
		} catch (error) {
			result.errors.push(error.message);
			console.error(`Cache warming job ${jobId} failed:`, error);
		} finally {
			this.isWarmingActive = false;
		}

		return result;
	}

	/**
	 * Warm popular content based on analytics
	 */
	private async warmPopularContent(): Promise<number> {
		let itemsWarmed = 0;

		// Warm popular words
		for (const word of this.popularContentMetrics.popularWords.slice(0, 50)) {
			try {
				const key = this.queryCache.generateQueryKey(
					'WordService',
					'searchWords',
					{ term: word.term, language: word.language },
					undefined
				);

				// Simulate warming (in real implementation, this would fetch from API)
				await this.enhancedCache.set(
					key,
					[{ id: 'mock', term: word.term, language: word.language }],
					60 * 60 * 2, // 2 hours
					CachePriority.HIGH
				);
				itemsWarmed++;
			} catch (error) {
				console.warn(`Failed to warm word ${word.term}:`, error);
			}
		}

		// Warm popular collections
		for (const collection of this.popularContentMetrics.popularCollections.slice(0, 20)) {
			try {
				const key = this.queryCache.generateQueryKey(
					'CollectionService',
					'getCollectionById',
					{ collectionId: collection.id },
					undefined
				);

				// Simulate warming
				await this.enhancedCache.set(
					key,
					{ id: collection.id, name: 'Popular Collection' },
					60 * 60 * 4, // 4 hours
					CachePriority.HIGH
				);
				itemsWarmed++;
			} catch (error) {
				console.warn(`Failed to warm collection ${collection.id}:`, error);
			}
		}

		return itemsWarmed;
	}

	/**
	 * Warm content based on user behavior patterns
	 */
	private async warmUserPatterns(): Promise<number> {
		let itemsWarmed = 0;

		for (const [userId, pattern] of this.userBehaviorPatterns.entries()) {
			// Skip inactive users
			if (Date.now() - pattern.lastActivity > 7 * 24 * 60 * 60 * 1000) continue;

			// Warm user's most accessed collections
			for (const collectionId of pattern.mostAccessedCollections.slice(0, 5)) {
				try {
					const key = this.queryCache.generateQueryKey(
						'CollectionService',
						'getCollectionById',
						{ collectionId },
						userId
					);

					// Simulate warming
					await this.enhancedCache.set(
						key,
						{ id: collectionId, user_id: userId },
						60 * 60, // 1 hour
						CachePriority.MEDIUM
					);
					itemsWarmed++;
				} catch (error) {
					console.warn(`Failed to warm user collection ${collectionId}:`, error);
				}
			}

			// Warm common search patterns
			for (const searchPattern of pattern.searchPatterns.slice(0, 3)) {
				for (const language of pattern.preferredLanguages) {
					try {
						const key = this.queryCache.generateQueryKey(
							'WordService',
							'searchWords',
							{ term: searchPattern, language },
							userId
						);

						// Simulate warming
						await this.enhancedCache.set(
							key,
							[{ id: 'mock', term: searchPattern, language }],
							60 * 30, // 30 minutes
							CachePriority.MEDIUM
						);
						itemsWarmed++;
					} catch (error) {
						console.warn(`Failed to warm search pattern ${searchPattern}:`, error);
					}
				}
			}
		}

		return itemsWarmed;
	}

	/**
	 * Warm common LLM responses
	 */
	private async warmLLMResponses(): Promise<number> {
		let itemsWarmed = 0;

		// Common vocabulary generation requests
		const commonTerms = ['hello', 'world', 'language', 'learn', 'practice'];
		const languages = [Language.EN, Language.VI];

		for (const sourceLanguage of languages) {
			for (const targetLanguage of languages) {
				if (sourceLanguage === targetLanguage) continue;

				for (const term of commonTerms) {
					try {
						// Simulate LLM response
						await this.llmCache.setCachedResponse(
							'generateWordDetails',
							term,
							'gpt-4',
							0.3,
							1000,
							[{ id: 'mock', term, language: targetLanguage }],
							500,
							{ sourceLanguage, targetLanguage }
						);
						itemsWarmed++;
					} catch (error) {
						console.warn(`Failed to warm LLM response for ${term}:`, error);
					}
				}
			}
		}

		return itemsWarmed;
	}

	/**
	 * Execute predictive warming based on time patterns and user activity
	 */
	private async executePredictiveWarming(): Promise<number> {
		const currentHour = new Date().getHours();

		// Check if current time matches any warming conditions
		const predictiveJob = this.warmingJobs.get('predictive-warming');
		if (!predictiveJob?.conditions?.timeRanges) return 0;

		const shouldWarm = predictiveJob.conditions.timeRanges.some((range) => {
			const start = parseInt(range.start.split(':')[0]);
			const end = parseInt(range.end.split(':')[0]);
			return currentHour >= start && currentHour <= end;
		});

		if (!shouldWarm) return 0;

		// Predict and warm content based on historical patterns
		let itemsWarmed = 0;

		// Warm trending searches
		for (const search of this.popularContentMetrics.trendingSearches.slice(0, 10)) {
			try {
				const key = this.queryCache.generateQueryKey(
					'WordService',
					'searchWords',
					{ term: search.query, language: search.language || Language.EN },
					undefined
				);

				await this.enhancedCache.set(
					key,
					[{ id: 'mock', term: search.query }],
					60 * 45, // 45 minutes
					CachePriority.MEDIUM
				);
				itemsWarmed++;
			} catch (error) {
				console.warn(`Failed to warm trending search ${search.query}:`, error);
			}
		}

		return itemsWarmed;
	}

	/**
	 * Update user behavior pattern
	 */
	updateUserBehaviorPattern(userId: string, pattern: Partial<UserBehaviorPattern>): void {
		const existing = this.userBehaviorPatterns.get(userId) || {
			userId,
			mostAccessedCollections: [],
			preferredLanguages: [Language.EN],
			activeTimeRanges: [],
			searchPatterns: [],
			lastActivity: Date.now(),
			sessionDuration: 0,
			deviceType: 'desktop',
		};

		this.userBehaviorPatterns.set(userId, { ...existing, ...pattern });
	}

	/**
	 * Update popular content metrics
	 */
	updatePopularContentMetrics(metrics: Partial<PopularContentMetrics>): void {
		this.popularContentMetrics = { ...this.popularContentMetrics, ...metrics };
	}

	/**
	 * Get warming job status
	 */
	getWarmingJobStatus(jobId: string): {
		exists: boolean;
		enabled: boolean;
		lastExecution?: number;
		nextExecution?: number;
		isRunning: boolean;
	} {
		const job = this.warmingJobs.get(jobId);
		return {
			exists: !!job,
			enabled: job?.enabled || false,
			isRunning: this.isWarmingActive,
			// In a real implementation, you'd track execution times
			lastExecution: undefined,
			nextExecution: undefined,
		};
	}

	/**
	 * Get all warming jobs
	 */
	getAllWarmingJobs(): CacheWarmingJobConfig[] {
		return Array.from(this.warmingJobs.values());
	}

	/**
	 * Execute all enabled warming jobs
	 */
	async executeAllWarmingJobs(): Promise<CacheWarmingResult[]> {
		const results: CacheWarmingResult[] = [];

		for (const job of this.warmingJobs.values()) {
			if (job.enabled) {
				try {
					const result = await this.executeWarmingJob(job.id);
					results.push(result);
				} catch (error) {
					console.error(`Failed to execute warming job ${job.id}:`, error);
				}
			}
		}

		return results;
	}

	/**
	 * Get cache warming statistics
	 */
	getWarmingStatistics(): {
		totalJobs: number;
		enabledJobs: number;
		isWarmingActive: boolean;
		userPatternsTracked: number;
		popularContentItems: number;
	} {
		return {
			totalJobs: this.warmingJobs.size,
			enabledJobs: Array.from(this.warmingJobs.values()).filter((j) => j.enabled).length,
			isWarmingActive: this.isWarmingActive,
			userPatternsTracked: this.userBehaviorPatterns.size,
			popularContentItems:
				this.popularContentMetrics.popularWords.length +
				this.popularContentMetrics.popularCollections.length +
				this.popularContentMetrics.trendingSearches.length,
		};
	}
}
