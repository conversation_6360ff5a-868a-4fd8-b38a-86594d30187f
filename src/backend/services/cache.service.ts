import Redis from 'ioredis';
import { LRUCache } from 'lru-cache';
import { createHash } from 'crypto';

// Cache configuration
const DEFAULT_TTL = 60 * 60 * 24; // 24 hours in seconds
const DEFAULT_MEMORY_CACHE_SIZE = 1000; // Number of items
const DEFAULT_REDIS_TTL = 60 * 60 * 24 * 7; // 7 days in seconds

// Cache tiers
export enum CacheTier {
	MEMORY = 'memory',
	REDIS = 'redis',
	DATABASE = 'database',
}

// Cache priority levels
export enum CachePriority {
	LOW = 1,
	MEDIUM = 2,
	HIGH = 3,
	CRITICAL = 4,
}

// Cache strategies
export enum CacheStrategy {
	CACHE_ASIDE = 'cache-aside',
	WRITE_THROUGH = 'write-through',
	WRITE_BEHIND = 'write-behind',
	REFRESH_AHEAD = 'refresh-ahead',
}

// Cache configuration interface
export interface CacheConfig {
	memory: {
		enabled: boolean;
		maxSize: number;
		ttl: number;
	};
	redis: {
		enabled: boolean;
		host: string;
		port: number;
		password?: string;
		db: number;
		ttl: number;
		keyPrefix: string;
	};
	compression: {
		enabled: boolean;
		threshold: number; // Compress data larger than this size (bytes)
	};
	analytics: {
		enabled: boolean;
		sampleRate: number; // Percentage of operations to track
	};
}

// Cache statistics interface
export interface CacheStats {
	memory: {
		hits: number;
		misses: number;
		hitRate: number;
		size: number;
		maxSize: number;
	};
	redis: {
		hits: number;
		misses: number;
		hitRate: number;
		connections: number;
	};
	overall: {
		totalHits: number;
		totalMisses: number;
		overallHitRate: number;
		averageResponseTime: number;
	};
}

// Cache entry metadata
export interface CacheEntry<T> {
	data: T;
	timestamp: number;
	ttl: number;
	priority: CachePriority;
	accessCount: number;
	lastAccessed: number;
	compressed: boolean;
	size: number;
}

// Cache warming job interface
export interface CacheWarmingJob {
	id: string;
	name: string;
	schedule: string; // Cron expression
	enabled: boolean;
	priority: CachePriority;
	warmingFunction: () => Promise<void>;
}

// Performance metrics interface
export interface PerformanceMetrics {
	operationType: string;
	tier: CacheTier;
	responseTime: number;
	timestamp: number;
	success: boolean;
	keyPattern: string;
}

/**
 * Enhanced Multi-Tier Cache Service
 * Provides intelligent caching with memory, Redis, and database tiers
 */
export class EnhancedCacheService {
	private memoryCache!: LRUCache<string, CacheEntry<any>>;
	private redisClient: Redis | null = null;
	private config!: CacheConfig;
	private stats!: CacheStats;
	private performanceMetrics: PerformanceMetrics[] = [];
	private warmingJobs: Map<string, CacheWarmingJob> = new Map();

	constructor(config?: Partial<CacheConfig>) {
		this.config = this.mergeConfig(config);
		this.initializeMemoryCache();
		this.initializeRedis();
		this.initializeStats();
	}

	/**
	 * Merge user config with defaults
	 */
	private mergeConfig(userConfig?: Partial<CacheConfig>): CacheConfig {
		const defaultConfig: CacheConfig = {
			memory: {
				enabled: true,
				maxSize: DEFAULT_MEMORY_CACHE_SIZE,
				ttl: DEFAULT_TTL,
			},
			redis: {
				enabled: process.env.REDIS_URL ? true : false,
				host: process.env.REDIS_HOST || 'localhost',
				port: parseInt(process.env.REDIS_PORT || '6379'),
				password: process.env.REDIS_PASSWORD,
				db: parseInt(process.env.REDIS_DB || '0'),
				ttl: DEFAULT_REDIS_TTL,
				keyPrefix: process.env.REDIS_KEY_PREFIX || 'vocab:',
			},
			compression: {
				enabled: true,
				threshold: 1024, // 1KB
			},
			analytics: {
				enabled: true,
				sampleRate: 10, // 10% sampling
			},
		};

		return {
			memory: { ...defaultConfig.memory, ...userConfig?.memory },
			redis: { ...defaultConfig.redis, ...userConfig?.redis },
			compression: { ...defaultConfig.compression, ...userConfig?.compression },
			analytics: { ...defaultConfig.analytics, ...userConfig?.analytics },
		};
	}

	/**
	 * Initialize memory cache
	 */
	private initializeMemoryCache(): void {
		if (!this.config.memory.enabled) return;

		this.memoryCache = new LRUCache({
			max: this.config.memory.maxSize,
			ttl: this.config.memory.ttl * 1000, // Convert to milliseconds
			updateAgeOnGet: true,
			allowStale: false,
		});
	}

	/**
	 * Initialize Redis connection
	 */
	private async initializeRedis(): Promise<void> {
		if (!this.config.redis.enabled) return;

		try {
			this.redisClient = new Redis({
				host: this.config.redis.host,
				port: this.config.redis.port,
				password: this.config.redis.password,
				db: this.config.redis.db,
				maxRetriesPerRequest: 3,
				lazyConnect: true,
			});

			await this.redisClient.connect();
			console.log('Redis cache connected successfully');
		} catch (error) {
			console.error('Failed to connect to Redis:', error);
			this.redisClient = null;
		}
	}

	/**
	 * Initialize statistics
	 */
	private initializeStats(): void {
		this.stats = {
			memory: {
				hits: 0,
				misses: 0,
				hitRate: 0,
				size: 0,
				maxSize: this.config.memory.maxSize,
			},
			redis: {
				hits: 0,
				misses: 0,
				hitRate: 0,
				connections: 0,
			},
			overall: {
				totalHits: 0,
				totalMisses: 0,
				overallHitRate: 0,
				averageResponseTime: 0,
			},
		};
	}

	/**
	 * Generate a secure cache key
	 */
	generateKey(prefix: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				result[key] = params[key];
				return result;
			}, {});

		const paramString = JSON.stringify(sortedParams);
		const hash = createHash('sha256').update(paramString).digest('hex').substring(0, 16);
		return `${this.config.redis.keyPrefix}${prefix}:${hash}`;
	}

	/**
	 * Compress data if it exceeds threshold
	 */
	private compressData(data: any): { data: any; compressed: boolean; size: number } {
		const serialized = JSON.stringify(data);
		const size = Buffer.byteLength(serialized, 'utf8');

		if (this.config.compression.enabled && size > this.config.compression.threshold) {
			// Note: In a real implementation, you'd use actual compression
			// For now, we'll just mark it as compressed
			return { data: serialized, compressed: true, size };
		}

		return { data: serialized, compressed: false, size };
	}

	/**
	 * Decompress data if needed
	 */
	private decompressData(entry: CacheEntry<any>): any {
		if (entry.compressed) {
			// Note: In a real implementation, you'd decompress here
			return JSON.parse(entry.data);
		}
		return JSON.parse(entry.data);
	}

	/**
	 * Record performance metrics
	 */
	private recordMetrics(
		operationType: string,
		tier: CacheTier,
		responseTime: number,
		success: boolean,
		keyPattern: string
	): void {
		if (!this.config.analytics.enabled) return;
		if (Math.random() * 100 > this.config.analytics.sampleRate) return;

		this.performanceMetrics.push({
			operationType,
			tier,
			responseTime,
			timestamp: Date.now(),
			success,
			keyPattern,
		});

		// Keep only last 1000 metrics to prevent memory bloat
		if (this.performanceMetrics.length > 1000) {
			this.performanceMetrics = this.performanceMetrics.slice(-1000);
		}
	}

	/**
	 * Get value from cache with multi-tier fallback
	 */
	async get<T>(key: string): Promise<T | null> {
		const startTime = Date.now();

		try {
			// Try memory cache first
			if (this.config.memory.enabled && this.memoryCache) {
				const memoryEntry = this.memoryCache.get(key);
				if (memoryEntry) {
					this.stats.memory.hits++;
					this.stats.overall.totalHits++;
					this.recordMetrics('get', CacheTier.MEMORY, Date.now() - startTime, true, key);

					// Update access metadata
					memoryEntry.accessCount++;
					memoryEntry.lastAccessed = Date.now();

					return this.decompressData(memoryEntry);
				}
				this.stats.memory.misses++;
			}

			// Try Redis cache
			if (this.redisClient) {
				try {
					const redisData = await this.redisClient.get(key);
					if (redisData) {
						const entry: CacheEntry<T> = JSON.parse(redisData);
						this.stats.redis.hits++;
						this.stats.overall.totalHits++;
						this.recordMetrics(
							'get',
							CacheTier.REDIS,
							Date.now() - startTime,
							true,
							key
						);

						// Promote to memory cache
						if (this.config.memory.enabled && this.memoryCache) {
							entry.accessCount++;
							entry.lastAccessed = Date.now();
							this.memoryCache.set(key, entry);
						}

						return this.decompressData(entry);
					}
					this.stats.redis.misses++;
				} catch (error) {
					console.error('Redis get error:', error);
				}
			}

			this.stats.overall.totalMisses++;
			this.recordMetrics('get', CacheTier.DATABASE, Date.now() - startTime, false, key);
			return null;
		} catch (error) {
			console.error('Cache get error:', error);
			this.recordMetrics('get', CacheTier.MEMORY, Date.now() - startTime, false, key);
			return null;
		}
	}

	/**
	 * Set value in cache with multi-tier storage
	 */
	async set<T>(
		key: string,
		value: T,
		ttl: number = DEFAULT_TTL,
		priority: CachePriority = CachePriority.MEDIUM
	): Promise<boolean> {
		const startTime = Date.now();

		try {
			const { data, compressed, size } = this.compressData(value);
			const entry: CacheEntry<T> = {
				data,
				timestamp: Date.now(),
				ttl,
				priority,
				accessCount: 0,
				lastAccessed: Date.now(),
				compressed,
				size,
			};

			let success = true;

			// Store in memory cache
			if (this.config.memory.enabled && this.memoryCache) {
				this.memoryCache.set(key, entry, { ttl: ttl * 1000 });
			}

			// Store in Redis cache
			if (this.redisClient) {
				try {
					await this.redisClient.setex(key, this.config.redis.ttl, JSON.stringify(entry));
				} catch (error) {
					console.error('Redis set error:', error);
					success = false;
				}
			}

			this.recordMetrics('set', CacheTier.MEMORY, Date.now() - startTime, success, key);
			return success;
		} catch (error) {
			console.error('Cache set error:', error);
			this.recordMetrics('set', CacheTier.MEMORY, Date.now() - startTime, false, key);
			return false;
		}
	}

	/**
	 * Delete value from all cache tiers
	 */
	async del(key: string): Promise<boolean> {
		const startTime = Date.now();
		let success = true;

		try {
			// Delete from memory cache
			if (this.config.memory.enabled && this.memoryCache) {
				this.memoryCache.delete(key);
			}

			// Delete from Redis cache
			if (this.redisClient) {
				try {
					await this.redisClient.del(key);
				} catch (error) {
					console.error('Redis delete error:', error);
					success = false;
				}
			}

			this.recordMetrics('del', CacheTier.MEMORY, Date.now() - startTime, success, key);
			return success;
		} catch (error) {
			console.error('Cache delete error:', error);
			this.recordMetrics('del', CacheTier.MEMORY, Date.now() - startTime, false, key);
			return false;
		}
	}

	/**
	 * Clear all caches
	 */
	async flush(): Promise<void> {
		try {
			// Clear memory cache
			if (this.config.memory.enabled && this.memoryCache) {
				this.memoryCache.clear();
			}

			// Clear Redis cache
			if (this.redisClient) {
				try {
					await this.redisClient.flushdb();
				} catch (error) {
					console.error('Redis flush error:', error);
				}
			}

			// Reset stats
			this.initializeStats();
		} catch (error) {
			console.error('Cache flush error:', error);
		}
	}

	/**
	 * Get comprehensive cache statistics
	 */
	getStats(): CacheStats {
		// Update memory cache stats
		if (this.config.memory.enabled && this.memoryCache) {
			this.stats.memory.size = this.memoryCache.size;
			this.stats.memory.hitRate =
				this.stats.memory.hits / (this.stats.memory.hits + this.stats.memory.misses) || 0;
		}

		// Update Redis stats
		this.stats.redis.hitRate =
			this.stats.redis.hits / (this.stats.redis.hits + this.stats.redis.misses) || 0;

		// Update overall stats
		this.stats.overall.overallHitRate =
			this.stats.overall.totalHits /
				(this.stats.overall.totalHits + this.stats.overall.totalMisses) || 0;

		// Calculate average response time
		if (this.performanceMetrics.length > 0) {
			const totalTime = this.performanceMetrics.reduce(
				(sum, metric) => sum + metric.responseTime,
				0
			);
			this.stats.overall.averageResponseTime = totalTime / this.performanceMetrics.length;
		}

		return { ...this.stats };
	}

	/**
	 * Get performance metrics
	 */
	getPerformanceMetrics(): PerformanceMetrics[] {
		return [...this.performanceMetrics];
	}

	/**
	 * Warm cache with frequently accessed data
	 */
	async warmCache(
		warmingFunction: () => Promise<Array<{ key: string; value: any; ttl?: number }>>
	): Promise<void> {
		try {
			const dataToWarm = await warmingFunction();

			for (const item of dataToWarm) {
				await this.set(item.key, item.value, item.ttl || DEFAULT_TTL, CachePriority.HIGH);
			}

			console.log(`Cache warmed with ${dataToWarm.length} items`);
		} catch (error) {
			console.error('Cache warming error:', error);
		}
	}

	/**
	 * Register a cache warming job
	 */
	registerWarmingJob(job: CacheWarmingJob): void {
		this.warmingJobs.set(job.id, job);
	}

	/**
	 * Execute a cache warming job
	 */
	async executeWarmingJob(jobId: string): Promise<void> {
		const job = this.warmingJobs.get(jobId);
		if (!job || !job.enabled) return;

		try {
			await job.warmingFunction();
			console.log(`Cache warming job '${job.name}' executed successfully`);
		} catch (error) {
			console.error(`Cache warming job '${job.name}' failed:`, error);
		}
	}

	/**
	 * Get cache health status
	 */
	getHealthStatus(): {
		memory: { status: string; details: any };
		redis: { status: string; details: any };
		overall: { status: string; details: any };
	} {
		const memoryStatus = this.config.memory.enabled
			? this.memoryCache
				? 'healthy'
				: 'error'
			: 'disabled';

		const redisStatus = this.config.redis.enabled
			? this.redisClient?.status === 'ready'
				? 'healthy'
				: 'error'
			: 'disabled';

		const overallStatus =
			memoryStatus === 'healthy' || redisStatus === 'healthy' ? 'healthy' : 'degraded';

		return {
			memory: {
				status: memoryStatus,
				details: {
					enabled: this.config.memory.enabled,
					size: this.memoryCache?.size || 0,
					maxSize: this.config.memory.maxSize,
				},
			},
			redis: {
				status: redisStatus,
				details: {
					enabled: this.config.redis.enabled,
					connected: this.redisClient?.status === 'ready',
					host: this.config.redis.host,
					port: this.config.redis.port,
				},
			},
			overall: {
				status: overallStatus,
				details: {
					hitRate: this.stats.overall.overallHitRate,
					averageResponseTime: this.stats.overall.averageResponseTime,
				},
			},
		};
	}

	/**
	 * Cleanup resources
	 */
	async cleanup(): Promise<void> {
		try {
			if (this.redisClient) {
				await this.redisClient.quit();
			}
		} catch (error) {
			console.error('Cache cleanup error:', error);
		}
	}
}

// Legacy CacheService for backward compatibility
export class CacheService {
	private enhancedCache: EnhancedCacheService;

	constructor(ttlSeconds: number = DEFAULT_TTL) {
		this.enhancedCache = new EnhancedCacheService({
			memory: { enabled: true, maxSize: DEFAULT_MEMORY_CACHE_SIZE, ttl: ttlSeconds },
			redis: { enabled: false, host: '', port: 0, db: 0, ttl: 0, keyPrefix: '' },
			compression: { enabled: false, threshold: 0 },
			analytics: { enabled: false, sampleRate: 0 },
		});
	}

	get<T>(key: string): T | null {
		// Convert async to sync for backward compatibility
		// Note: This is not ideal, but maintains the original interface
		let result: T | null = null;
		this.enhancedCache.get<T>(key).then((value) => (result = value));
		return result;
	}

	set<T>(key: string, value: T, ttl: number = DEFAULT_TTL): boolean {
		// Convert async to sync for backward compatibility
		let success = false;
		this.enhancedCache.set(key, value, ttl).then((result) => (success = result));
		return success;
	}

	del(key: string): boolean {
		// Convert async to sync for backward compatibility
		let success = false;
		this.enhancedCache.del(key).then((result) => (success = result));
		return success;
	}

	flush(): void {
		this.enhancedCache.flush();
	}

	getStats() {
		return this.enhancedCache.getStats();
	}

	generateKey(prefix: string, params: Record<string, any>): string {
		return this.enhancedCache.generateKey(prefix, params);
	}
}
