import { EnhancedCacheService, CachePriority } from './cache.service';
import { createHash } from 'crypto';

// Cache invalidation patterns
export interface CacheInvalidationPattern {
	pattern: string;
	dependencies: string[];
	ttl: number;
	priority: CachePriority;
}

// Query cache configuration
export interface QueryCacheConfig {
	defaultTtl: number;
	maxKeyLength: number;
	enableInvalidation: boolean;
	patterns: CacheInvalidationPattern[];
}

// Cache key metadata
export interface CacheKeyMetadata {
	pattern: string;
	dependencies: string[];
	createdAt: number;
	lastAccessed: number;
	accessCount: number;
}

/**
 * Query Cache Service for intelligent database query caching
 */
export class QueryCacheService {
	private cacheService: EnhancedCacheService;
	private config: QueryCacheConfig;
	private keyMetadata: Map<string, CacheKeyMetadata> = new Map();
	private dependencyMap: Map<string, Set<string>> = new Map();

	constructor(cacheService: EnhancedCacheService, config?: Partial<QueryCacheConfig>) {
		this.cacheService = cacheService;
		this.config = this.mergeConfig(config);
		this.initializePatterns();
	}

	private mergeConfig(userConfig?: Partial<QueryCacheConfig>): QueryCacheConfig {
		const defaultConfig: QueryCacheConfig = {
			defaultTtl: 60 * 60, // 1 hour
			maxKeyLength: 250,
			enableInvalidation: true,
			patterns: [
				{
					pattern: 'word:search:*',
					dependencies: ['word:*'],
					ttl: 60 * 30, // 30 minutes
					priority: CachePriority.HIGH,
				},
				{
					pattern: 'collection:*',
					dependencies: ['collection:*', 'word:*'],
					ttl: 60 * 60, // 1 hour
					priority: CachePriority.HIGH,
				},
				{
					pattern: 'user:progress:*',
					dependencies: ['user:*', 'collection:*'],
					ttl: 60 * 15, // 15 minutes
					priority: CachePriority.MEDIUM,
				},
				{
					pattern: 'llm:response:*',
					dependencies: [],
					ttl: 60 * 60 * 24, // 24 hours
					priority: CachePriority.CRITICAL,
				},
			],
		};

		return {
			...defaultConfig,
			...userConfig,
			patterns: [...defaultConfig.patterns, ...(userConfig?.patterns || [])],
		};
	}

	private initializePatterns(): void {
		for (const pattern of this.config.patterns) {
			for (const dependency of pattern.dependencies) {
				if (!this.dependencyMap.has(dependency)) {
					this.dependencyMap.set(dependency, new Set());
				}
				this.dependencyMap.get(dependency)!.add(pattern.pattern);
			}
		}
	}

	/**
	 * Generate a cache key for a query
	 */
	generateQueryKey(
		service: string,
		method: string,
		params: Record<string, any>,
		userId?: string
	): string {
		const keyData = {
			service,
			method,
			params: this.normalizeParams(params),
			...(userId && { userId }),
		};

		const keyString = JSON.stringify(keyData);
		const hash = createHash('sha256').update(keyString).digest('hex').substring(0, 16);
		const key = `query:${service}:${method}:${hash}`;

		// Ensure key length doesn't exceed limit
		return key.length > this.config.maxKeyLength ? `query:${hash}` : key;
	}

	/**
	 * Normalize parameters for consistent caching
	 */
	private normalizeParams(params: Record<string, any>): Record<string, any> {
		const normalized: Record<string, any> = {};

		for (const [key, value] of Object.entries(params)) {
			if (value === undefined || value === null) continue;

			if (Array.isArray(value)) {
				normalized[key] = [...value].sort();
			} else if (typeof value === 'object') {
				normalized[key] = this.normalizeParams(value);
			} else {
				normalized[key] = value;
			}
		}

		return normalized;
	}

	/**
	 * Get cached query result
	 */
	async getCachedQuery<T>(
		service: string,
		method: string,
		params: Record<string, any>,
		userId?: string
	): Promise<T | null> {
		const key = this.generateQueryKey(service, method, params, userId);
		const result = await this.cacheService.get<T>(key);

		if (result) {
			// Update metadata
			const metadata = this.keyMetadata.get(key);
			if (metadata) {
				metadata.lastAccessed = Date.now();
				metadata.accessCount++;
			}
		}

		return result;
	}

	/**
	 * Cache query result
	 */
	async setCachedQuery<T>(
		service: string,
		method: string,
		params: Record<string, any>,
		result: T,
		userId?: string,
		customTtl?: number
	): Promise<void> {
		const key = this.generateQueryKey(service, method, params, userId);
		const pattern = this.findMatchingPattern(key);
		const ttl = customTtl || pattern?.ttl || this.config.defaultTtl;
		const priority = pattern?.priority || CachePriority.MEDIUM;

		await this.cacheService.set(key, result, ttl, priority);

		// Store metadata
		this.keyMetadata.set(key, {
			pattern: pattern?.pattern || 'unknown',
			dependencies: pattern?.dependencies || [],
			createdAt: Date.now(),
			lastAccessed: Date.now(),
			accessCount: 1,
		});
	}

	/**
	 * Find matching cache pattern for a key
	 */
	private findMatchingPattern(key: string): CacheInvalidationPattern | undefined {
		return this.config.patterns.find((pattern) => {
			const regex = new RegExp(pattern.pattern.replace(/\*/g, '.*'));
			return regex.test(key);
		});
	}

	/**
	 * Invalidate cache based on dependency patterns
	 */
	async invalidateByPattern(pattern: string): Promise<void> {
		if (!this.config.enableInvalidation) return;

		const affectedPatterns = this.dependencyMap.get(pattern) || new Set();
		const keysToInvalidate: string[] = [];

		for (const [key, metadata] of this.keyMetadata.entries()) {
			if (affectedPatterns.has(metadata.pattern)) {
				keysToInvalidate.push(key);
			}
		}

		// Batch invalidation
		for (const key of keysToInvalidate) {
			await this.cacheService.del(key);
			this.keyMetadata.delete(key);
		}

		console.log(`Invalidated ${keysToInvalidate.length} cache entries for pattern: ${pattern}`);
	}

	/**
	 * Invalidate cache for specific entity
	 */
	async invalidateEntity(entityType: string, entityId: string): Promise<void> {
		const pattern = `${entityType}:${entityId}`;
		await this.invalidateByPattern(pattern);
	}

	/**
	 * Get cache statistics for queries
	 */
	getQueryCacheStats(): {
		totalKeys: number;
		patternDistribution: Record<string, number>;
		averageAccessCount: number;
		oldestEntry: number;
		newestEntry: number;
	} {
		const patternDistribution: Record<string, number> = {};
		let totalAccessCount = 0;
		let oldestEntry = Date.now();
		let newestEntry = 0;

		for (const metadata of this.keyMetadata.values()) {
			patternDistribution[metadata.pattern] =
				(patternDistribution[metadata.pattern] || 0) + 1;
			totalAccessCount += metadata.accessCount;
			oldestEntry = Math.min(oldestEntry, metadata.createdAt);
			newestEntry = Math.max(newestEntry, metadata.createdAt);
		}

		return {
			totalKeys: this.keyMetadata.size,
			patternDistribution,
			averageAccessCount:
				this.keyMetadata.size > 0 ? totalAccessCount / this.keyMetadata.size : 0,
			oldestEntry,
			newestEntry,
		};
	}

	/**
	 * Clean up expired metadata
	 */
	cleanupMetadata(): void {
		const now = Date.now();
		const expiredKeys: string[] = [];

		for (const [key, metadata] of this.keyMetadata.entries()) {
			const pattern = this.findMatchingPattern(key);
			const ttl = pattern?.ttl || this.config.defaultTtl;

			if (now - metadata.createdAt > ttl * 1000) {
				expiredKeys.push(key);
			}
		}

		for (const key of expiredKeys) {
			this.keyMetadata.delete(key);
		}
	}
}

/**
 * Cache decorator for automatic query caching
 */
// Note: Decorators removed for build compatibility
// Use manual caching in service implementations instead
