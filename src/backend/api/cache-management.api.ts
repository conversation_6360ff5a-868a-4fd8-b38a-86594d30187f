import { NextRequest, NextResponse } from 'next/server';
import {
	getCacheAnalyticsService,
	getCacheWarmingService,
	getEnhancedCacheService,
	getQueryCacheService,
	getLLMCacheService,
} from '@/backend/wire';

/**
 * Cache Management API
 * Provides endpoints for cache monitoring, analytics, and management
 */

/**
 * GET /api/cache/stats
 * Get comprehensive cache statistics
 */
export async function getCacheStats(_request: NextRequest): Promise<NextResponse> {
	try {
		const analyticsService = getCacheAnalyticsService();
		const warmingService = getCacheWarmingService();
		const enhancedCache = getEnhancedCacheService();
		const queryCache = getQueryCacheService();
		const llmCache = getLLMCacheService();

		const stats = {
			current: analyticsService.getCurrentStats(),
			health: analyticsService.generateHealthReport(),
			warming: warmingService.getWarmingStatistics(),
			trends: analyticsService.getPerformanceTrends(24),
			usagePatterns: analyticsService.getUsagePatterns().slice(0, 10),
			cacheHealth: {
				enhanced: enhancedCache.getHealthStatus(),
				query: queryCache.getQueryCacheStats(),
				llm: llmCache.getLLMCacheHealth(),
			},
		};

		return NextResponse.json(stats);
	} catch (error) {
		console.error('Failed to get cache stats:', error);
		return NextResponse.json({ error: 'Failed to retrieve cache statistics' }, { status: 500 });
	}
}

/**
 * GET /api/cache/health
 * Get cache health status
 */
export async function getCacheHealth(_request: NextRequest): Promise<NextResponse> {
	try {
		const analyticsService = getCacheAnalyticsService();
		const health = analyticsService.generateHealthReport();

		return NextResponse.json(health);
	} catch (error) {
		console.error('Failed to get cache health:', error);
		return NextResponse.json({ error: 'Failed to retrieve cache health' }, { status: 500 });
	}
}

/**
 * GET /api/cache/metrics
 * Get cache performance metrics with optional time range
 */
export async function getCacheMetrics(request: NextRequest): Promise<NextResponse> {
	try {
		const { searchParams } = new URL(request.url);
		const hours = parseInt(searchParams.get('hours') || '24');
		const format = searchParams.get('format') || 'json';

		const analyticsService = getCacheAnalyticsService();

		if (format === 'csv') {
			const csvData = analyticsService.exportMetrics('csv');
			return new NextResponse(csvData, {
				headers: {
					'Content-Type': 'text/csv',
					'Content-Disposition': 'attachment; filename=cache-metrics.csv',
				},
			});
		}

		const trends = analyticsService.getPerformanceTrends(hours);
		const current = analyticsService.getCurrentStats();

		return NextResponse.json({
			current,
			trends,
			timeRange: `${hours} hours`,
		});
	} catch (error) {
		console.error('Failed to get cache metrics:', error);
		return NextResponse.json({ error: 'Failed to retrieve cache metrics' }, { status: 500 });
	}
}

/**
 * GET /api/cache/recommendations
 * Get cache optimization recommendations
 */
export async function getCacheRecommendations(_request: NextRequest): Promise<NextResponse> {
	try {
		const analyticsService = getCacheAnalyticsService();
		const recommendations = analyticsService.generateOptimizationRecommendations();

		return NextResponse.json({
			recommendations,
			generatedAt: Date.now(),
		});
	} catch (error) {
		console.error('Failed to get cache recommendations:', error);
		return NextResponse.json(
			{ error: 'Failed to retrieve cache recommendations' },
			{ status: 500 }
		);
	}
}

/**
 * POST /api/cache/warm
 * Execute cache warming jobs
 */
export async function executeCacheWarming(request: NextRequest): Promise<NextResponse> {
	try {
		const body = await request.json();
		const { jobId, jobIds } = body;

		const warmingService = getCacheWarmingService();
		let results;

		if (jobId) {
			// Execute single job
			results = [await warmingService.executeWarmingJob(jobId)];
		} else if (jobIds && Array.isArray(jobIds)) {
			// Execute multiple jobs
			results = [];
			for (const id of jobIds) {
				try {
					const result = await warmingService.executeWarmingJob(id);
					results.push(result);
				} catch (error) {
					results.push({
						jobId: id,
						error: error.message,
						startTime: Date.now(),
						endTime: Date.now(),
						itemsWarmed: 0,
						cacheHitImprovement: 0,
						errors: [error.message],
						performance: {
							averageWarmTime: 0,
							totalDataSize: 0,
							cacheEfficiency: 0,
						},
					});
				}
			}
		} else {
			// Execute all enabled jobs
			results = await warmingService.executeAllWarmingJobs();
		}

		return NextResponse.json({
			results,
			executedAt: Date.now(),
		});
	} catch (error) {
		console.error('Failed to execute cache warming:', error);
		return NextResponse.json({ error: 'Failed to execute cache warming' }, { status: 500 });
	}
}

/**
 * GET /api/cache/warming/jobs
 * Get all cache warming jobs
 */
export async function getCacheWarmingJobs(_request: NextRequest): Promise<NextResponse> {
	try {
		const warmingService = getCacheWarmingService();
		const jobs = warmingService.getAllWarmingJobs();

		const jobsWithStatus = jobs.map((job) => ({
			...job,
			status: warmingService.getWarmingJobStatus(job.id),
		}));

		return NextResponse.json({
			jobs: jobsWithStatus,
			statistics: warmingService.getWarmingStatistics(),
		});
	} catch (error) {
		console.error('Failed to get warming jobs:', error);
		return NextResponse.json({ error: 'Failed to retrieve warming jobs' }, { status: 500 });
	}
}

/**
 * POST /api/cache/clear
 * Clear cache with optional filters
 */
export async function clearCache(request: NextRequest): Promise<NextResponse> {
	try {
		const body = await request.json();
		const { type, pattern } = body;

		const enhancedCache = getEnhancedCacheService();
		const queryCache = getQueryCacheService();
		const llmCache = getLLMCacheService();

		let clearedItems = 0;

		switch (type) {
			case 'all':
				await enhancedCache.flush();
				await queryCache.flush?.();
				await llmCache.clearLLMCache();
				clearedItems = -1; // Indicates all cleared
				break;

			case 'memory':
				await enhancedCache.flush();
				clearedItems = -1;
				break;

			case 'query':
				if (pattern) {
					await queryCache.invalidateByPattern(pattern);
				} else {
					await queryCache.flush?.();
				}
				clearedItems = -1;
				break;

			case 'llm':
				if (pattern) {
					await llmCache.invalidateLLMOperation(pattern);
				} else {
					await llmCache.clearLLMCache();
				}
				clearedItems = -1;
				break;

			default:
				throw new Error(`Unknown cache type: ${type}`);
		}

		return NextResponse.json({
			success: true,
			type,
			pattern,
			clearedItems,
			clearedAt: Date.now(),
		});
	} catch (error) {
		console.error('Failed to clear cache:', error);
		return NextResponse.json({ error: 'Failed to clear cache' }, { status: 500 });
	}
}

/**
 * GET /api/cache/usage-patterns
 * Get detailed cache usage patterns
 */
export async function getCacheUsagePatterns(request: NextRequest): Promise<NextResponse> {
	try {
		const { searchParams } = new URL(request.url);
		const limit = parseInt(searchParams.get('limit') || '50');

		const analyticsService = getCacheAnalyticsService();
		const patterns = analyticsService.getUsagePatterns().slice(0, limit);

		return NextResponse.json({
			patterns,
			totalPatterns: patterns.length,
			analyzedAt: Date.now(),
		});
	} catch (error) {
		console.error('Failed to get usage patterns:', error);
		return NextResponse.json({ error: 'Failed to retrieve usage patterns' }, { status: 500 });
	}
}

/**
 * POST /api/cache/optimize
 * Execute cache optimization
 */
export async function optimizeCache(_request: NextRequest): Promise<NextResponse> {
	try {
		const analyticsService = getCacheAnalyticsService();

		// Get current stats before optimization
		const beforeStats = analyticsService.getCurrentStats();

		// Execute optimization (this would include cleanup, rebalancing, etc.)
		const optimizationResults = {
			expiredEntriesCleared: 0, // Would be implemented
			memoryDefragmented: true,
			indexesOptimized: true,
			cacheRebalanced: true,
		};

		// Get stats after optimization
		const afterStats = analyticsService.getCurrentStats();

		const improvement =
			afterStats && beforeStats
				? {
						hitRateImprovement:
							afterStats.overall.hitRate - beforeStats.overall.hitRate,
						responseTimeImprovement:
							beforeStats.overall.averageResponseTime -
							afterStats.overall.averageResponseTime,
				  }
				: null;

		return NextResponse.json({
			success: true,
			optimizationResults,
			improvement,
			optimizedAt: Date.now(),
		});
	} catch (error) {
		console.error('Failed to optimize cache:', error);
		return NextResponse.json({ error: 'Failed to optimize cache' }, { status: 500 });
	}
}

/**
 * GET /api/cache/dashboard
 * Get comprehensive dashboard data
 */
export async function getCacheDashboard(_request: NextRequest): Promise<NextResponse> {
	try {
		const analyticsService = getCacheAnalyticsService();
		const warmingService = getCacheWarmingService();
		const enhancedCache = getEnhancedCacheService();

		const dashboard = {
			overview: {
				health: analyticsService.generateHealthReport(),
				currentStats: analyticsService.getCurrentStats(),
				trends: analyticsService.getPerformanceTrends(6), // Last 6 hours
			},
			performance: {
				hitRates: analyticsService.getPerformanceTrends(24).hitRateTrend,
				responseTimes: analyticsService.getPerformanceTrends(24).responseTimeTrend,
				costSavings: analyticsService.getPerformanceTrends(24).costSavingsTrend,
			},
			warming: {
				statistics: warmingService.getWarmingStatistics(),
				jobs: warmingService.getAllWarmingJobs().map((job) => ({
					...job,
					status: warmingService.getWarmingJobStatus(job.id),
				})),
			},
			recommendations: analyticsService.generateOptimizationRecommendations(),
			usagePatterns: analyticsService.getUsagePatterns().slice(0, 5),
			systemHealth: enhancedCache.getHealthStatus(),
		};

		return NextResponse.json(dashboard);
	} catch (error) {
		console.error('Failed to get cache dashboard:', error);
		return NextResponse.json({ error: 'Failed to retrieve cache dashboard' }, { status: 500 });
	}
}
