'use client';

import React, { useState } from 'react';
import { useCacheManagement, useCacheHealthMonitoring } from '@/hooks/use-cache-management';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
	Activity, 
	AlertTriangle, 
	CheckCircle, 
	Clock, 
	Database, 
	RefreshCw, 
	Settings, 
	TrendingUp,
	Zap,
	BarChart3,
	Timer,
	DollarSign
} from 'lucide-react';

/**
 * Cache Dashboard Component
 * Provides comprehensive cache monitoring and management interface
 */
export function CacheDashboard() {
	const {
		stats,
		health,
		warmingJobs,
		recommendations,
		usagePatterns,
		isLoading,
		error,
		refreshStats,
		executeWarmingJob,
		executeAllWarmingJobs,
		clearCache,
		optimizeCache,
		enableRealTimeUpdates,
		isRealTimeEnabled,
	} = useCacheManagement();

	const { alerts, isHealthy, isCritical } = useCacheHealthMonitoring();
	const [activeTab, setActiveTab] = useState('overview');

	if (isLoading) {
		return (
			<div className="flex items-center justify-center p-8">
				<RefreshCw className="h-8 w-8 animate-spin" />
				<span className="ml-2">Loading cache dashboard...</span>
			</div>
		);
	}

	if (error) {
		return (
			<Alert variant="destructive">
				<AlertTriangle className="h-4 w-4" />
				<AlertTitle>Error</AlertTitle>
				<AlertDescription>{error}</AlertDescription>
			</Alert>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Cache Management Dashboard</h1>
					<p className="text-muted-foreground">
						Monitor and optimize application cache performance
					</p>
				</div>
				<div className="flex items-center space-x-2">
					<Button
						variant="outline"
						size="sm"
						onClick={() => enableRealTimeUpdates(!isRealTimeEnabled)}
					>
						<Activity className="h-4 w-4 mr-2" />
						{isRealTimeEnabled ? 'Disable' : 'Enable'} Real-time
					</Button>
					<Button variant="outline" size="sm" onClick={refreshStats}>
						<RefreshCw className="h-4 w-4 mr-2" />
						Refresh
					</Button>
				</div>
			</div>

			{/* Health Alerts */}
			{alerts.length > 0 && (
				<Alert variant={isCritical ? "destructive" : "default"}>
					<AlertTriangle className="h-4 w-4" />
					<AlertTitle>Cache Health Alerts</AlertTitle>
					<AlertDescription>
						<ul className="list-disc list-inside space-y-1">
							{alerts.map((alert, index) => (
								<li key={index}>{alert}</li>
							))}
						</ul>
					</AlertDescription>
				</Alert>
			)}

			{/* Overview Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Overall Health</CardTitle>
						{isHealthy ? (
							<CheckCircle className="h-4 w-4 text-green-600" />
						) : (
							<AlertTriangle className="h-4 w-4 text-red-600" />
						)}
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{health?.overall || 'Unknown'}
						</div>
						<Badge variant={isHealthy ? "default" : "destructive"}>
							{health?.overall || 'Unknown'}
						</Badge>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
						<TrendingUp className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{stats?.current?.overall?.hitRate 
								? `${(stats.current.overall.hitRate * 100).toFixed(1)}%`
								: 'N/A'
							}
						</div>
						<Progress 
							value={(stats?.current?.overall?.hitRate || 0) * 100} 
							className="mt-2"
						/>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Response Time</CardTitle>
						<Timer className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{stats?.current?.overall?.averageResponseTime 
								? `${stats.current.overall.averageResponseTime.toFixed(0)}ms`
								: 'N/A'
							}
						</div>
						<p className="text-xs text-muted-foreground">
							Average response time
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
						<DollarSign className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							${stats?.current?.llmCache?.costSavings?.toFixed(2) || '0.00'}
						</div>
						<p className="text-xs text-muted-foreground">
							LLM API cost savings
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Main Content Tabs */}
			<Tabs value={activeTab} onValueChange={setActiveTab}>
				<TabsList className="grid w-full grid-cols-5">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="performance">Performance</TabsTrigger>
					<TabsTrigger value="warming">Cache Warming</TabsTrigger>
					<TabsTrigger value="recommendations">Recommendations</TabsTrigger>
					<TabsTrigger value="management">Management</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-4">
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
						{/* Component Health */}
						<Card>
							<CardHeader>
								<CardTitle>Component Health</CardTitle>
								<CardDescription>Status of individual cache components</CardDescription>
							</CardHeader>
							<CardContent className="space-y-3">
								{health?.components && Object.entries(health.components).map(([component, status]) => (
									<div key={component} className="flex items-center justify-between">
										<span className="capitalize">{component} Cache</span>
										<Badge variant={
											status === 'healthy' ? 'default' :
											status === 'warning' ? 'secondary' :
											status === 'offline' ? 'outline' : 'destructive'
										}>
											{status}
										</Badge>
									</div>
								))}
							</CardContent>
						</Card>

						{/* Usage Patterns */}
						<Card>
							<CardHeader>
								<CardTitle>Top Usage Patterns</CardTitle>
								<CardDescription>Most frequently accessed cache patterns</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									{usagePatterns.slice(0, 5).map((pattern, index) => (
										<div key={index} className="flex items-center justify-between text-sm">
											<span className="truncate">{pattern.pattern}</span>
											<Badge variant="outline">{pattern.frequency}</Badge>
										</div>
									))}
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="performance" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Performance Metrics</CardTitle>
							<CardDescription>Cache performance over time</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div className="text-center">
									<div className="text-2xl font-bold text-blue-600">
										{stats?.current?.memoryCache?.hitRate 
											? `${(stats.current.memoryCache.hitRate * 100).toFixed(1)}%`
											: 'N/A'
										}
									</div>
									<p className="text-sm text-muted-foreground">Memory Hit Rate</p>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-green-600">
										{stats?.current?.redisCache?.hitRate 
											? `${(stats.current.redisCache.hitRate * 100).toFixed(1)}%`
											: 'N/A'
										}
									</div>
									<p className="text-sm text-muted-foreground">Redis Hit Rate</p>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-purple-600">
										{stats?.current?.llmCache?.hitRate 
											? `${(stats.current.llmCache.hitRate * 100).toFixed(1)}%`
											: 'N/A'
										}
									</div>
									<p className="text-sm text-muted-foreground">LLM Hit Rate</p>
								</div>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="warming" className="space-y-4">
					<div className="flex items-center justify-between">
						<h3 className="text-lg font-medium">Cache Warming Jobs</h3>
						<Button onClick={executeAllWarmingJobs}>
							<Zap className="h-4 w-4 mr-2" />
							Execute All Jobs
						</Button>
					</div>
					
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{warmingJobs.map((job) => (
							<Card key={job.id}>
								<CardHeader>
									<CardTitle className="text-base">{job.name}</CardTitle>
									<CardDescription>
										Schedule: {job.schedule} | Priority: {job.priority}
									</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="flex items-center justify-between">
										<Badge variant={job.enabled ? "default" : "secondary"}>
											{job.enabled ? 'Enabled' : 'Disabled'}
										</Badge>
										<Button
											size="sm"
											onClick={() => executeWarmingJob(job.id)}
											disabled={!job.enabled}
										>
											<Clock className="h-4 w-4 mr-2" />
											Execute
										</Button>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</TabsContent>

				<TabsContent value="recommendations" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Optimization Recommendations</CardTitle>
							<CardDescription>AI-powered suggestions to improve cache performance</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								{recommendations.map((rec, index) => (
									<div key={index} className="border rounded-lg p-4">
										<div className="flex items-center justify-between mb-2">
											<h4 className="font-medium">{rec.title}</h4>
											<Badge variant={
												rec.priority === 'critical' ? 'destructive' :
												rec.priority === 'high' ? 'secondary' : 'outline'
											}>
												{rec.priority}
											</Badge>
										</div>
										<p className="text-sm text-muted-foreground mb-2">{rec.description}</p>
										<p className="text-sm"><strong>Impact:</strong> {rec.impact}</p>
										<p className="text-sm"><strong>Implementation:</strong> {rec.implementation}</p>
										<p className="text-sm"><strong>Estimated Improvement:</strong> {rec.estimatedImprovement}%</p>
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="management" className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<Card>
							<CardHeader>
								<CardTitle>Cache Operations</CardTitle>
								<CardDescription>Manage cache data and performance</CardDescription>
							</CardHeader>
							<CardContent className="space-y-2">
								<Button 
									variant="outline" 
									className="w-full"
									onClick={optimizeCache}
								>
									<Settings className="h-4 w-4 mr-2" />
									Optimize Cache
								</Button>
								<Button 
									variant="outline" 
									className="w-full"
									onClick={() => clearCache('memory')}
								>
									<Database className="h-4 w-4 mr-2" />
									Clear Memory Cache
								</Button>
								<Button 
									variant="outline" 
									className="w-full"
									onClick={() => clearCache('query')}
								>
									<BarChart3 className="h-4 w-4 mr-2" />
									Clear Query Cache
								</Button>
								<Button 
									variant="destructive" 
									className="w-full"
									onClick={() => clearCache('all')}
								>
									<AlertTriangle className="h-4 w-4 mr-2" />
									Clear All Caches
								</Button>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>System Information</CardTitle>
								<CardDescription>Current cache system status</CardDescription>
							</CardHeader>
							<CardContent className="space-y-2">
								<div className="flex justify-between text-sm">
									<span>Memory Usage:</span>
									<span>{stats?.current?.memoryCache?.size || 0} / {stats?.current?.memoryCache?.maxSize || 0}</span>
								</div>
								<div className="flex justify-between text-sm">
									<span>Redis Connections:</span>
									<span>{stats?.current?.redisCache?.connections || 0}</span>
								</div>
								<div className="flex justify-between text-sm">
									<span>Query Cache Keys:</span>
									<span>{stats?.current?.queryCache?.totalKeys || 0}</span>
								</div>
								<div className="flex justify-between text-sm">
									<span>LLM Requests:</span>
									<span>{stats?.current?.llmCache?.totalRequests || 0}</span>
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
