'use client';

import { useEffect } from 'react';
import { initializeCacheServices } from '@/lib/cache-initialization';

/**
 * Cache Initializer Component
 * Initializes cache services on client-side mount
 */
export function CacheInitializer() {
	useEffect(() => {
		// Initialize cache services when component mounts
		initializeCacheServices();

		// Cleanup on unmount
		return () => {
			// Cleanup would be handled by the services themselves
		};
	}, []);

	// This component doesn't render anything
	return null;
}
