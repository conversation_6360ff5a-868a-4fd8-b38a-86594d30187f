'use client';

import { useEffect } from 'react';

/**
 * Cache Initializer Component
 * Initializes cache services on client-side mount
 */
export function CacheInitializer() {
	useEffect(() => {
		// Initialize cache services via API call to avoid server-side imports
		const initializeCache = async () => {
			try {
				// Call API to initialize cache services on server
				const response = await fetch('/api/cache/init', { method: 'POST' });
				if (response.ok) {
					console.log('Cache services initialized via API');
				} else {
					console.warn('Cache initialization API returned error');
				}
			} catch (error) {
				console.warn('Failed to initialize cache services:', error);
			}
		};

		// Delay initialization to avoid blocking initial render
		setTimeout(initializeCache, 1000);
	}, []);

	// This component doesn't render anything
	return null;
}
