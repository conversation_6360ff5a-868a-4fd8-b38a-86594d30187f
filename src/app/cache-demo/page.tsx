'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCacheManagement } from '@/hooks/use-cache-management';

/**
 * Cache Demo Page
 * Demonstrates cache functionality and provides testing interface
 */
export default function CacheDemoPage() {
	const {
		stats,
		health,
		warmingJobs,
		isLoading,
		error,
		refreshStats,
		executeWarmingJob,
		clearCache,
		optimizeCache,
	} = useCacheManagement();

	const [testResults, setTestResults] = useState<string[]>([]);

	const addTestResult = (result: string) => {
		setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
	};

	const testCacheOperations = async () => {
		addTestResult('Starting cache operations test...');
		
		try {
			// Test cache warming
			addTestResult('Testing cache warming...');
			if (warmingJobs.length > 0) {
				await executeWarmingJob(warmingJobs[0].id);
				addTestResult('✅ Cache warming completed');
			}

			// Test cache optimization
			addTestResult('Testing cache optimization...');
			await optimizeCache();
			addTestResult('✅ Cache optimization completed');

			// Test stats refresh
			addTestResult('Testing stats refresh...');
			await refreshStats();
			addTestResult('✅ Stats refresh completed');

			addTestResult('🎉 All cache operations completed successfully!');
		} catch (err) {
			addTestResult(`❌ Error: ${err.message}`);
		}
	};

	const clearTestResults = () => {
		setTestResults([]);
	};

	if (isLoading) {
		return (
			<div className="container mx-auto py-6">
				<div className="text-center">Loading cache demo...</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-6 space-y-6">
			<div className="text-center">
				<h1 className="text-3xl font-bold mb-2">Cache System Demo</h1>
				<p className="text-muted-foreground">
					Test and demonstrate the sophisticated caching system
				</p>
			</div>

			{error && (
				<Card className="border-red-200 bg-red-50">
					<CardHeader>
						<CardTitle className="text-red-800">Error</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-red-700">{error}</p>
					</CardContent>
				</Card>
			)}

			{/* Quick Stats */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<Card>
					<CardHeader>
						<CardTitle className="text-sm">Cache Health</CardTitle>
					</CardHeader>
					<CardContent>
						<Badge variant={health?.overall === 'healthy' ? 'default' : 'destructive'}>
							{health?.overall || 'Unknown'}
						</Badge>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="text-sm">Hit Rate</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{stats?.current?.overall?.hitRate 
								? `${(stats.current.overall.hitRate * 100).toFixed(1)}%`
								: 'N/A'
							}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="text-sm">Warming Jobs</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{warmingJobs.length}</div>
						<p className="text-xs text-muted-foreground">
							{warmingJobs.filter(j => j.enabled).length} enabled
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Test Operations */}
			<Card>
				<CardHeader>
					<CardTitle>Test Cache Operations</CardTitle>
					<CardDescription>
						Run various cache operations to test the system
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="flex flex-wrap gap-2">
						<Button onClick={testCacheOperations}>
							Run All Tests
						</Button>
						<Button variant="outline" onClick={refreshStats}>
							Refresh Stats
						</Button>
						<Button variant="outline" onClick={optimizeCache}>
							Optimize Cache
						</Button>
						<Button 
							variant="outline" 
							onClick={() => clearCache('memory')}
						>
							Clear Memory
						</Button>
						<Button 
							variant="destructive" 
							onClick={() => clearCache('all')}
						>
							Clear All
						</Button>
					</div>

					{/* Test Results */}
					{testResults.length > 0 && (
						<div className="mt-4">
							<div className="flex items-center justify-between mb-2">
								<h4 className="font-medium">Test Results</h4>
								<Button size="sm" variant="outline" onClick={clearTestResults}>
									Clear Results
								</Button>
							</div>
							<div className="bg-gray-50 p-3 rounded-md max-h-60 overflow-y-auto">
								{testResults.map((result, index) => (
									<div key={index} className="text-sm font-mono mb-1">
										{result}
									</div>
								))}
							</div>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Warming Jobs */}
			<Card>
				<CardHeader>
					<CardTitle>Cache Warming Jobs</CardTitle>
					<CardDescription>
						Available cache warming jobs and their status
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{warmingJobs.map((job) => (
							<Card key={job.id} className="border">
								<CardHeader className="pb-2">
									<CardTitle className="text-base">{job.name}</CardTitle>
									<CardDescription className="text-xs">
										{job.schedule} | Priority: {job.priority}
									</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="flex items-center justify-between">
										<Badge variant={job.enabled ? "default" : "secondary"}>
											{job.enabled ? 'Enabled' : 'Disabled'}
										</Badge>
										<Button
											size="sm"
											onClick={() => {
												executeWarmingJob(job.id);
												addTestResult(`Executed warming job: ${job.name}`);
											}}
											disabled={!job.enabled}
										>
											Execute
										</Button>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Navigation */}
			<Card>
				<CardHeader>
					<CardTitle>Navigation</CardTitle>
					<CardDescription>
						Access other parts of the application
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-2">
					<Button 
						variant="outline" 
						className="w-full"
						onClick={() => window.open('/admin/cache', '_blank')}
					>
						Open Full Cache Dashboard
					</Button>
					<Button 
						variant="outline" 
						className="w-full"
						onClick={() => window.location.href = '/collections'}
					>
						Go to Collections
					</Button>
					<Button 
						variant="outline" 
						className="w-full"
						onClick={() => window.location.href = '/'}
					>
						Go to Home
					</Button>
				</CardContent>
			</Card>
		</div>
	);
}
