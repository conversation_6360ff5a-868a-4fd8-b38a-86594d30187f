import 'server-only';
import {
	getEnhancedCacheService,
	getQueryCacheService,
	getLLMCacheService,
	getCacheWarmingService,
	getCacheAnalyticsService,
} from '@/backend/wire';

/**
 * Initialize cache services on application startup
 */
export async function initializeCacheServices(): Promise<void> {
	try {
		console.log('Initializing cache services...');

		// Initialize core cache services
		const warmingService = getCacheWarmingService();
		const analyticsService = getCacheAnalyticsService();

		// Start cache warming for popular content
		setTimeout(async () => {
			try {
				await warmingService.executeWarmingJob('warm-popular-content');
				console.log('Initial cache warming completed');
			} catch (error) {
				console.warn('Initial cache warming failed:', error);
			}
		}, 5000); // Wait 5 seconds after startup

		// Start analytics collection
		setTimeout(() => {
			try {
				analyticsService.collectMetrics();
				console.log('Cache analytics started');
			} catch (error) {
				console.warn('Cache analytics initialization failed:', error);
			}
		}, 2000); // Wait 2 seconds after startup

		console.log('Cache services initialized successfully');
	} catch (error) {
		console.error('Failed to initialize cache services:', error);
	}
}

/**
 * Cleanup cache services on application shutdown
 */
export async function cleanupCacheServices(): Promise<void> {
	try {
		console.log('Cleaning up cache services...');

		const enhancedCache = getEnhancedCacheService();
		await enhancedCache.cleanup();

		console.log('Cache services cleaned up successfully');
	} catch (error) {
		console.error('Failed to cleanup cache services:', error);
	}
}

/**
 * Health check for cache services
 */
export async function checkCacheHealth(): Promise<{
	status: 'healthy' | 'degraded' | 'critical';
	details: any;
}> {
	try {
		const enhancedCache = getEnhancedCacheService();
		const analyticsService = getCacheAnalyticsService();

		const healthStatus = enhancedCache.getHealthStatus();
		const currentStats = analyticsService.getCurrentStats();

		let status: 'healthy' | 'degraded' | 'critical' = 'healthy';

		if (healthStatus.overall.status === 'degraded') {
			status = 'degraded';
		} else if (healthStatus.overall.status === 'critical') {
			status = 'critical';
		}

		return {
			status,
			details: {
				health: healthStatus,
				stats: currentStats,
			},
		};
	} catch (error) {
		console.error('Cache health check failed:', error);
		return {
			status: 'critical',
			details: { error: error instanceof Error ? error.message : 'Unknown error' },
		};
	}
}
