import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
	env: {},
	reactStrictMode: false,
	images: {
		unoptimized: true,
	},
	webpack: (config, { isServer }) => {
		// Exclude server-only modules from client bundle
		if (!isServer) {
			config.resolve.fallback = {
				...config.resolve.fallback,
				dns: false,
				net: false,
				tls: false,
				fs: false,
				crypto: false,
				stream: false,
				url: false,
				zlib: false,
				http: false,
				https: false,
				assert: false,
				os: false,
				path: false,
			};
		}
		return config;
	},
};

export default nextConfig;
